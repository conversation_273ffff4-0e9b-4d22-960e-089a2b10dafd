import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/account_provider.dart';
import '../providers/post_provider.dart';
import '../providers/proxy_provider.dart' as proxy_provider;
import '../providers/schedule_provider.dart';
import '../providers/log_provider.dart';
import '../models/models.dart';
import '../utils/app_localizations.dart';
import '../utils/facebook_categories.dart';
import 'package:file_picker/file_picker.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const AccountsScreen(),
    const PostsScreen(),
    const ProxiesScreen(),
    const SchedulerScreen(),
    const LogsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: appProvider.isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: Row(
          children: [
            // الشريط الجانبي
            NavigationRail(
              selectedIndex: _selectedIndex,
              onDestinationSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              extended: true,
              minExtendedWidth: 200,
              backgroundColor: Theme.of(context).colorScheme.surface,
              elevation: 2,
              leading: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.store,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      localizations.appTitle,
                      style: Theme.of(context).textTheme.titleSmall,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              trailing: Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر تبديل الثيم
                        IconButton(
                          onPressed: () => appProvider.toggleTheme(),
                          icon: Icon(
                            appProvider.themeMode == ThemeMode.dark
                                ? Icons.light_mode
                                : Icons.dark_mode,
                          ),
                          tooltip: appProvider.themeMode == ThemeMode.dark
                              ? 'الوضع الفاتح'
                              : 'الوضع الداكن',
                        ),
                        const SizedBox(height: 8),
                        // زر تبديل اللغة
                        IconButton(
                          onPressed: () => appProvider.toggleLanguage(),
                          icon: const Icon(Icons.language),
                          tooltip: localizations.language,
                        ),
                        const SizedBox(height: 8),
                        // زر الإعدادات
                        IconButton(
                          onPressed: () => _showSettingsDialog(context),
                          icon: const Icon(Icons.settings),
                          tooltip: localizations.settings,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              destinations: [
                NavigationRailDestination(
                  icon: const Icon(Icons.dashboard),
                  selectedIcon: const Icon(Icons.dashboard),
                  label: Text(localizations.dashboard),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.account_circle),
                  selectedIcon: const Icon(Icons.account_circle),
                  label: Text(localizations.accounts),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.post_add),
                  selectedIcon: const Icon(Icons.post_add),
                  label: Text(localizations.posts),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.security),
                  selectedIcon: const Icon(Icons.security),
                  label: Text(localizations.proxies),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.schedule),
                  selectedIcon: const Icon(Icons.schedule),
                  label: Text(localizations.scheduler),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.list_alt),
                  selectedIcon: const Icon(Icons.list_alt),
                  label: Text(localizations.logs),
                ),
              ],
            ),
            
            // خط فاصل
            const VerticalDivider(thickness: 1, width: 1),
            
            // المحتوى الرئيسي
            Expanded(
              child: _screens[_selectedIndex],
            ),
          ],
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.settings),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // إعداد المظهر
            ListTile(
              leading: const Icon(Icons.palette),
              title: Text(localizations.theme),
              trailing: DropdownButton<ThemeMode>(
                value: appProvider.themeMode,
                onChanged: (ThemeMode? mode) {
                  if (mode != null) {
                    appProvider.setThemeMode(mode);
                  }
                },
                items: [
                  DropdownMenuItem(
                    value: ThemeMode.system,
                    child: Text(localizations.systemTheme),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.light,
                    child: Text(localizations.lightTheme),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.dark,
                    child: Text(localizations.darkTheme),
                  ),
                ],
              ),
            ),
            
            // إعداد اللغة
            ListTile(
              leading: const Icon(Icons.language),
              title: Text(localizations.language),
              trailing: DropdownButton<String>(
                value: appProvider.locale.languageCode,
                onChanged: (String? languageCode) {
                  if (languageCode != null) {
                    final locale = languageCode == 'ar' 
                        ? const Locale('ar', 'SA')
                        : const Locale('en', 'US');
                    appProvider.setLocale(locale);
                  }
                },
                items: [
                  DropdownMenuItem(
                    value: 'ar',
                    child: Text(localizations.arabic),
                  ),
                  DropdownMenuItem(
                    value: 'en',
                    child: Text(localizations.english),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
        ],
      ),
    );
  }
}

// شاشات مؤقتة بسيطة
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث البيانات عند فتح لوحة التحكم
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshAllData();
    });
  }

  void _refreshAllData() {
    Provider.of<AccountProvider>(context, listen: false).syncWithApi();
    Provider.of<PostProvider>(context, listen: false).syncWithApi();
    Provider.of<proxy_provider.ProxyProvider>(context, listen: false).syncWithApi();
    Provider.of<ScheduleProvider>(context, listen: false).syncWithApi();
    Provider.of<LogProvider>(context, listen: false).syncWithApi();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.dashboard),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: _refreshAllData,
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب
            _buildWelcomeCard(context),
            const SizedBox(height: 24),

            // إحصائيات سريعة
            Text(
              'نظرة عامة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildQuickStats(context),
            const SizedBox(height: 24),

            // الأنشطة الحديثة والجدولات القادمة
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الجدولات القادمة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildUpcomingSchedules(context),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'السجلات الحديثة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildRecentLogs(context),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // حالة النظام
            Text(
              'حالة النظام',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildSystemStatus(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.dashboard,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً بك في نظام أتمتة Facebook Marketplace',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'إدارة شاملة لحساباتك ومنشوراتك مع أتمتة ذكية',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'آخر تحديث: ${DateTime.now().toString().substring(0, 19)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Consumer4<AccountProvider, PostProvider, proxy_provider.ProxyProvider, ScheduleProvider>(
      builder: (context, accountProvider, postProvider, proxyProvider, scheduleProvider, child) {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'الحسابات النشطة',
                accountProvider.activeAccountsCount.toString(),
                '${accountProvider.totalAccounts} إجمالي',
                Icons.people,
                Colors.green,
                null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                context,
                'المنشورات المجدولة',
                postProvider.scheduledPostsCount.toString(),
                '${postProvider.totalPosts} إجمالي',
                Icons.schedule,
                Colors.blue,
                null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                context,
                'البروكسيات النشطة',
                proxyProvider.activeProxiesCount.toString(),
                '${proxyProvider.totalProxies} إجمالي',
                Icons.security,
                Colors.purple,
                null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                context,
                'الجدولات النشطة',
                scheduleProvider.activeSchedulesCount.toString(),
                '${scheduleProvider.totalSchedules} إجمالي',
                Icons.event,
                Colors.orange,
                null,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, String subtitle,
      IconData icon, Color color, VoidCallback? onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
                ],
              ),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      value,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpcomingSchedules(BuildContext context) {
    return Consumer<ScheduleProvider>(
      builder: (context, scheduleProvider, child) {
        final upcomingSchedules = scheduleProvider.getUpcomingSchedules().take(5).toList();

        if (upcomingSchedules.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.schedule_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لا توجد جدولات قادمة',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: upcomingSchedules.map((schedule) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              schedule.name,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              schedule.nextRun != null
                                  ? _formatDateTime(schedule.nextRun!)
                                  : 'غير محدد',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentLogs(BuildContext context) {
    return Consumer<LogProvider>(
      builder: (context, logProvider, child) {
        final recentLogs = logProvider.getRecentLogs().take(5).toList();

        if (recentLogs.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.list_alt_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لا توجد سجلات حديثة',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: recentLogs.map((log) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _getLogLevelColor(log.level),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              log.message,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              _formatDateTime(log.timestamp),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getLogLevelColor(log.level).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          log.level.displayName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getLogLevelColor(log.level),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemStatus(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildStatusItem(
              context,
              'خادم Backend',
              'متصل',
              Icons.cloud_done,
              Colors.green,
            ),
            const Divider(),
            _buildStatusItem(
              context,
              'WebSocket',
              'متصل',
              Icons.wifi,
              Colors.green,
            ),
            const Divider(),
            _buildStatusItem(
              context,
              'قاعدة البيانات المحلية',
              'تعمل',
              Icons.storage,
              Colors.green,
            ),
            const Divider(),
            _buildStatusItem(
              context,
              'نظام التشفير',
              'نشط',
              Icons.security,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(BuildContext context, String title, String status,
      IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            status,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Color _getLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.error:
        return Colors.red;
      case LogLevel.warn:
        return Colors.orange;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.debug:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AccountProvider>(context, listen: false).syncWithApi();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.accounts),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showAddAccountDialog(context),
            icon: const Icon(Icons.add),
            tooltip: localizations.addAccount,
          ),
          IconButton(
            onPressed: () {
              Provider.of<AccountProvider>(context, listen: false).syncWithApi();
            },
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
      ),
      body: Consumer<AccountProvider>(
        builder: (context, accountProvider, child) {
          if (accountProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (accountProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    accountProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => accountProvider.syncWithApi(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات الحسابات
              _buildAccountStats(context, accountProvider),

              // قائمة الحسابات
              Expanded(
                child: accountProvider.accounts.isEmpty
                    ? _buildEmptyState(context)
                    : _buildAccountsList(context, accountProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAccountStats(BuildContext context, AccountProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              localizations.activeAccounts,
              provider.activeAccountsCount.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              localizations.inactiveAccounts,
              provider.inactiveAccountsCount.toString(),
              Icons.pause_circle,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              localizations.bannedAccounts,
              provider.bannedAccountsCount.toString(),
              Icons.block,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              context,
              'إجمالي الحسابات',
              provider.totalAccounts.toString(),
              Icons.people,
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد حسابات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة حساب Facebook الأول',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddAccountDialog(context),
            icon: const Icon(Icons.add),
            label: Text(localizations.addAccount),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsList(BuildContext context, AccountProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.accounts.length,
      itemBuilder: (context, index) {
        final account = provider.accounts[index];
        return _buildAccountCard(context, account, provider);
      },
    );
  }

  Widget _buildAccountCard(BuildContext context, Account account, AccountProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(account.status),
                  child: Icon(
                    Icons.person,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        account.email,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(context, account.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'آخر تسجيل دخول',
                    account.lastLogin != null
                        ? _formatDateTime(account.lastLogin!)
                        : 'لم يتم تسجيل الدخول',
                    Icons.login,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'عدد المنشورات',
                    account.postCount.toString(),
                    Icons.post_add,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _testAccount(context, account, provider),
                  icon: const Icon(Icons.play_arrow),
                  label: Text(localizations.testAccount),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showEditAccountDialog(context, account),
                  icon: const Icon(Icons.edit),
                  label: Text(localizations.edit),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteAccount(context, account, provider),
                  icon: const Icon(Icons.delete),
                  label: Text(localizations.delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, AccountStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatusColor(status)),
      ),
      child: Text(
        status.displayName,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getStatusColor(status),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(AccountStatus status) {
    switch (status) {
      case AccountStatus.active:
        return Colors.green;
      case AccountStatus.inactive:
        return Colors.orange;
      case AccountStatus.banned:
        return Colors.red;
      case AccountStatus.suspended:
        return Colors.purple;
      case AccountStatus.testing:
        return Colors.blue;
      case AccountStatus.error:
        return Colors.red.shade700;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showAddAccountDialog(BuildContext context) {
    _showAccountDialog(context, null);
  }

  void _showEditAccountDialog(BuildContext context, Account account) {
    _showAccountDialog(context, account);
  }

  void _showAccountDialog(BuildContext context, Account? account) {
    final localizations = AppLocalizations.of(context)!;
    final isEditing = account != null;

    final nameController = TextEditingController(text: account?.name ?? '');
    final emailController = TextEditingController(text: account?.email ?? '');
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'تعديل الحساب' : localizations.addAccount),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: localizations.accountName,
                  prefixIcon: const Icon(Icons.person),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: emailController,
                decoration: InputDecoration(
                  labelText: localizations.email,
                  prefixIcon: const Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: passwordController,
                decoration: InputDecoration(
                  labelText: localizations.password,
                  prefixIcon: const Icon(Icons.lock),
                ),
                obscureText: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () => _saveAccount(context, account, nameController.text,
                emailController.text, passwordController.text),
            child: Text(localizations.save),
          ),
        ],
      ),
    );
  }

  void _saveAccount(BuildContext context, Account? existingAccount,
      String name, String email, String password) {
    if (name.isEmpty || email.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جميع الحقول مطلوبة')),
      );
      return;
    }

    final provider = Provider.of<AccountProvider>(context, listen: false);

    if (existingAccount != null) {
      // تعديل حساب موجود
      final updatedAccount = existingAccount.copyWith(
        name: name,
        email: email,
        encryptedPassword: password, // سيتم تشفيرها في الـ provider
      );
      provider.updateAccount(updatedAccount, password);
    } else {
      // إضافة حساب جديد
      final newAccount = Account(
        name: name,
        email: email,
        encryptedPassword: password, // سيتم تشفيرها في الـ provider
      );
      provider.addAccount(newAccount, password);
    }

    Navigator.of(context).pop();
  }

  void _testAccount(BuildContext context, Account account, AccountProvider provider) async {
    if (account.id == null) return;

    // عرض حوار التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('جاري اختبار الحساب...'),
            const SizedBox(height: 8),
            Text(
              'يتم التحقق من صحة بيانات تسجيل الدخول',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );

    try {
      final result = await provider.testAccount(account.id!);
      final success = result['success'] ?? false;
      final message = result['message'] ?? '';
      final facebookStatus = result['facebookStatus'] ?? 'unknown';
      final details = result['details'] ?? {};

      // إغلاق حوار التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        // عرض حوار النتيجة التفصيلي
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: success ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(success ? 'نجح الاختبار' : 'فشل الاختبار'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('اسم الحساب: ${account.name}'),
                Text('البريد الإلكتروني: ${account.email}'),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: success ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: success ? Colors.green : Colors.red,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        success ? '✅ نتيجة الاختبار:' : '❌ نتيجة الاختبار:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: success ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        success
                          ? 'تم تسجيل الدخول بنجاح\nالحساب نشط ويمكن استخدامه للنشر'
                          : 'فشل في تسجيل الدخول\nتحقق من صحة البيانات أو حالة الحساب',
                        style: TextStyle(
                          color: success ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ),
                if (success) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تم تحديث حالة الحساب إلى "نشط"',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
              if (!success)
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showEditAccountDialog(context, account);
                  },
                  child: const Text('تعديل البيانات'),
                ),
            ],
          ),
        );
      }
    } catch (e) {
      // إغلاق حوار التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار الحساب: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _deleteAccount(BuildContext context, Account account, AccountProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الحساب'),
        content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (account.id != null) {
                provider.deleteAccount(account.id!);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}

class PostsScreen extends StatefulWidget {
  const PostsScreen({super.key});

  @override
  State<PostsScreen> createState() => _PostsScreenState();
}

class _PostsScreenState extends State<PostsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PostProvider>(context, listen: false).syncWithApi();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.posts),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showAddPostDialog(context),
            icon: const Icon(Icons.add),
            tooltip: localizations.addPost,
          ),
          IconButton(
            onPressed: () {
              Provider.of<PostProvider>(context, listen: false).syncWithApi();
            },
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'الكل'),
            Tab(text: localizations.draftPosts),
            Tab(text: localizations.scheduledPosts),
            Tab(text: localizations.templates),
          ],
        ),
      ),
      body: Consumer<PostProvider>(
        builder: (context, postProvider, child) {
          if (postProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (postProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    postProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => postProvider.syncWithApi(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات المنشورات
              _buildPostStats(context, postProvider),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPostsList(context, postProvider.posts, postProvider),
                    _buildPostsList(context, postProvider.getPostsByStatus(PostStatus.draft), postProvider),
                    _buildPostsList(context, postProvider.getScheduledPosts(), postProvider),
                    _buildPostsList(context, postProvider.getTemplates(), postProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPostStats(BuildContext context, PostProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildPostStatCard(
              context,
              'المسودات',
              provider.draftPostsCount.toString(),
              Icons.edit_note,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildPostStatCard(
              context,
              'مجدولة',
              provider.scheduledPostsCount.toString(),
              Icons.schedule,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildPostStatCard(
              context,
              'منشورة',
              provider.publishedPostsCount.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildPostStatCard(
              context,
              'القوالب',
              provider.templatesCount.toString(),
              Icons.library_books,
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostStatCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList(BuildContext context, List<Post> posts, PostProvider provider) {
    if (posts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.post_add_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد منشورات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإنشاء منشور جديد',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return _buildPostCard(context, post, provider);
      },
    );
  }

  Widget _buildPostCard(BuildContext context, Post post, PostProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // صورة المنشور أو أيقونة افتراضية
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  child: post.images.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Icon(Icons.image, size: 30),
                        )
                      : Icon(
                          Icons.post_add,
                          size: 30,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        post.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            CurrencyUtils.formatPrice(post.price),
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            post.category,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                _buildPostStatusChip(context, post.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPostInfoItem(
                    context,
                    'الحسابات',
                    '${post.accountIds.length} حساب',
                    Icons.people,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPostInfoItem(
                    context,
                    'الصور',
                    '${post.images.length} صورة',
                    Icons.image,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPostInfoItem(
                    context,
                    'تاريخ الإنشاء',
                    _formatDateTime(post.createdAt),
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (post.status == PostStatus.draft) ...[
                  TextButton.icon(
                    onPressed: () => _publishPost(context, post, provider),
                    icon: const Icon(Icons.publish),
                    label: Text('نشر'),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _schedulePost(context, post, provider),
                    icon: const Icon(Icons.schedule),
                    label: Text('جدولة'),
                  ),
                  const SizedBox(width: 8),
                ],
                if (!post.isTemplate) ...[
                  TextButton.icon(
                    onPressed: () => _saveAsTemplate(context, post, provider),
                    icon: const Icon(Icons.save),
                    label: Text('حفظ كقالب'),
                  ),
                  const SizedBox(width: 8),
                ],
                TextButton.icon(
                  onPressed: () => _showEditPostDialog(context, post),
                  icon: const Icon(Icons.edit),
                  label: Text(localizations.edit),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deletePost(context, post, provider),
                  icon: const Icon(Icons.delete),
                  label: Text(localizations.delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostStatusChip(BuildContext context, PostStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getPostStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getPostStatusColor(status)),
      ),
      child: Text(
        status.displayName,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getPostStatusColor(status),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPostInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getPostStatusColor(PostStatus status) {
    switch (status) {
      case PostStatus.draft:
        return Colors.orange;
      case PostStatus.scheduled:
        return Colors.blue;
      case PostStatus.publishing:
        return Colors.purple;
      case PostStatus.published:
        return Colors.green;
      case PostStatus.failed:
        return Colors.red;
      case PostStatus.paused:
        return Colors.grey;
    }
  }

  void _showAddPostDialog(BuildContext context) {
    _showPostDialog(context, null);
  }

  void _showEditPostDialog(BuildContext context, Post post) {
    _showPostDialog(context, post);
  }

  void _showPostDialog(BuildContext context, Post? post) {
    final localizations = AppLocalizations.of(context)!;
    final isEditing = post != null;

    final titleController = TextEditingController(text: post?.title ?? '');
    final descriptionController = TextEditingController(text: post?.description ?? '');
    final priceController = TextEditingController(text: post?.price.toString() ?? '');
    final locationController = TextEditingController(text: post?.location ?? '');

    String selectedCategory = post?.category ?? FacebookCategories.getDefaultCategory();
    List<String> selectedImagePaths = [];
    List<PlatformFile> selectedImageFiles = []; // For web compatibility

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'تعديل المنشور' : localizations.addPost),
        content: SizedBox(
          width: 500,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: 'عنوان المنشور',
                    prefixIcon: const Icon(Icons.title),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: 'وصف المنشور',
                    prefixIcon: const Icon(Icons.description),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  maxLines: 4,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: priceController,
                        decoration: InputDecoration(
                          labelText: 'السعر (${CurrencyUtils.egyptianPound})',
                          suffixText: CurrencyUtils.egyptianPound,
                          prefixIcon: const Icon(Icons.attach_money),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: StatefulBuilder(
                        builder: (context, setState) {
                          return DropdownButtonFormField<String>(
                            value: selectedCategory,
                            decoration: InputDecoration(
                              labelText: 'الفئة',
                              prefixIcon: const Icon(Icons.category),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            items: FacebookCategories.categories.map((category) {
                              return DropdownMenuItem<String>(
                                value: category,
                                child: Text(
                                  category,
                                  style: const TextStyle(fontSize: 14),
                                ),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  selectedCategory = newValue;
                                });
                              }
                            },
                            isExpanded: true,
                            menuMaxHeight: 300,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: locationController,
                  decoration: InputDecoration(
                    labelText: 'الموقع',
                    prefixIcon: const Icon(Icons.location_on),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // قسم اختيار الصور
                StatefulBuilder(
                  builder: (context, setState) {
                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.image,
                            size: 48,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'إضافة صور المنشور',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: () async {
                              try {
                                FilePickerResult? result = await FilePicker.platform.pickFiles(
                                  type: FileType.image,
                                  allowMultiple: true,
                                );

                                if (result != null) {
                                  setState(() {
                                    // For web, use file names instead of paths
                                    selectedImagePaths = result.files
                                        .where((file) => file.bytes != null)
                                        .map((file) => file.name)
                                        .toList();

                                    // Store the actual file data for web
                                    selectedImageFiles = result.files
                                        .where((file) => file.bytes != null)
                                        .toList();
                                  });
                                }
                              } catch (e) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('خطأ في اختيار الصور: $e')),
                                  );
                                }
                              }
                            },
                            icon: const Icon(Icons.add_photo_alternate),
                            label: Text('اختيار صور (${selectedImagePaths.length})'),
                          ),
                          if (selectedImagePaths.isNotEmpty) ...[
                            const SizedBox(height: 12),
                            SizedBox(
                              height: 100,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: selectedImagePaths.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: Stack(
                                      children: [
                                        Container(
                                          width: 80,
                                          height: 80,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(8),
                                            color: Colors.grey[300],
                                          ),
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: Icon(
                                              Icons.image,
                                              size: 40,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          top: -8,
                                          right: -8,
                                          child: IconButton(
                                            onPressed: () {
                                              setState(() {
                                                selectedImagePaths.removeAt(index);
                                              });
                                            },
                                            icon: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                              child: const Icon(
                                                Icons.close,
                                                color: Colors.white,
                                                size: 16,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'تم اختيار ${selectedImagePaths.length} صورة',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () => _savePost(
              context,
              post,
              titleController.text,
              descriptionController.text,
              priceController.text,
              selectedCategory,
              locationController.text,
              selectedImagePaths,
            ),
            child: Text(localizations.save),
          ),
        ],
      ),
    );
  }

  void _savePost(BuildContext context, Post? existingPost, String title,
      String description, String priceText, String category, String location, List<String> imagePaths) {
    if (title.isEmpty || description.isEmpty || priceText.isEmpty || category.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جميع الحقول الأساسية مطلوبة')),
      );
      return;
    }

    final price = double.tryParse(priceText);
    if (price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال سعر صحيح')),
      );
      return;
    }

    final provider = Provider.of<PostProvider>(context, listen: false);

    if (existingPost != null) {
      // تعديل منشور موجود
      final updatedPost = existingPost.copyWith(
        title: title,
        description: description,
        price: price,
        category: category,
        location: location,
      );
      provider.updatePost(updatedPost);
    } else {
      // إضافة منشور جديد
      final List<PostImage> postImages = imagePaths.map((path) {
        final fileName = path.split('/').last;
        return PostImage(
          filename: fileName,
          originalName: fileName,
          path: path,
          size: 0, // سيتم حساب الحجم لاحقاً
          isUrl: false,
        );
      }).toList();

      final newPost = Post(
        title: title,
        description: description,
        price: price,
        category: category,
        location: location,
        status: PostStatus.draft,
        accountIds: [], // سيتم اختيار الحسابات لاحقاً
        images: postImages,
      );
      provider.addPost(newPost);
    }

    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(existingPost != null ? 'تم تحديث المنشور بنجاح' : 'تم إضافة المنشور بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _publishPost(BuildContext context, Post post, PostProvider provider) async {
    if (post.id == null) return;

    final success = await provider.publishPost(post.id!);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم نشر المنشور بنجاح' : 'فشل في نشر المنشور'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _schedulePost(BuildContext context, Post post, PostProvider provider) async {
    final DateTime? selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'اختر تاريخ النشر',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (selectedDate == null) return;

    if (!context.mounted) return;

    final TimeOfDay? selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(DateTime.now().add(const Duration(hours: 1))),
      helpText: 'اختر وقت النشر',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (selectedTime == null) return;

    final DateTime scheduledDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      selectedTime.hour,
      selectedTime.minute,
    );

    // التحقق من أن الوقت المحدد في المستقبل
    if (scheduledDateTime.isBefore(DateTime.now())) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب أن يكون وقت النشر في المستقبل'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // عرض حوار التأكيد
    if (context.mounted) {
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد جدولة المنشور'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('سيتم نشر المنشور في:'),
              const SizedBox(height: 8),
              Text(
                '📅 التاريخ: ${scheduledDateTime.day}/${scheduledDateTime.month}/${scheduledDateTime.year}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '🕐 الوقت: ${scheduledDateTime.hour.toString().padLeft(2, '0')}:${scheduledDateTime.minute.toString().padLeft(2, '0')}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('عنوان المنشور: ${post.title}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('تأكيد الجدولة'),
            ),
          ],
        ),
      );

      if (confirmed == true && post.id != null) {
        try {
          await provider.schedulePost(post.id!, scheduledDateTime);

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم جدولة المنشور بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في جدولة المنشور: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    }
  }

  void _saveAsTemplate(BuildContext context, Post post, PostProvider provider) async {
    final templateName = await _showTemplateNameDialog(context);
    if (templateName != null && post.id != null) {
      await provider.saveAsTemplate(post.id!, templateName);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ القالب بنجاح')),
        );
      }
    }
  }

  Future<String?> _showTemplateNameDialog(BuildContext context) async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اسم القالب'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'أدخل اسم القالب',
            prefixIcon: Icon(Icons.label),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deletePost(BuildContext context, Post post, PostProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنشور'),
        content: Text('هل أنت متأكد من حذف منشور "${post.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (post.id != null) {
                provider.deletePost(post.id!);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

class ProxiesScreen extends StatefulWidget {
  const ProxiesScreen({super.key});

  @override
  State<ProxiesScreen> createState() => _ProxiesScreenState();
}

class _ProxiesScreenState extends State<ProxiesScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<proxy_provider.ProxyProvider>(context, listen: false).syncWithApi();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.proxies),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showAddProxyDialog(context),
            icon: const Icon(Icons.add),
            tooltip: localizations.addProxy,
          ),
          IconButton(
            onPressed: () => _testAllProxies(context),
            icon: const Icon(Icons.play_arrow),
            tooltip: localizations.testAllProxies,
          ),
          IconButton(
            onPressed: () {
              Provider.of<proxy_provider.ProxyProvider>(context, listen: false).syncWithApi();
            },
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
      ),
      body: Consumer<proxy_provider.ProxyProvider>(
        builder: (context, proxyProvider, child) {
          if (proxyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (proxyProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    proxyProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => proxyProvider.syncWithApi(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات البروكسيات
              _buildProxyStats(context, proxyProvider),

              // قائمة البروكسيات
              Expanded(
                child: proxyProvider.proxies.isEmpty
                    ? _buildEmptyState(context)
                    : _buildProxiesList(context, proxyProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProxyStats(BuildContext context, proxy_provider.ProxyProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildProxyStatCard(
              context,
              localizations.activeProxies,
              provider.activeProxiesCount.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildProxyStatCard(
              context,
              localizations.failedProxies,
              provider.failedProxiesCount.toString(),
              Icons.error,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildProxyStatCard(
              context,
              'غير مختبرة',
              provider.untestedProxiesCount.toString(),
              Icons.help_outline,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildProxyStatCard(
              context,
              'إجمالي البروكسيات',
              provider.totalProxies.toString(),
              Icons.security,
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProxyStatCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.security_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بروكسيات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة بروكسي للحماية والأمان',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddProxyDialog(context),
            icon: const Icon(Icons.add),
            label: Text(localizations.addProxy),
          ),
        ],
      ),
    );
  }

  Widget _buildProxiesList(BuildContext context, proxy_provider.ProxyProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.proxies.length,
      itemBuilder: (context, index) {
        final proxy = provider.proxies[index];
        return _buildProxyCard(context, proxy, provider);
      },
    );
  }

  Widget _buildProxyCard(BuildContext context, ProxyModel proxy, proxy_provider.ProxyProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getProxyStatusColor(proxy.status),
                  child: Icon(
                    Icons.security,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        proxy.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${proxy.host}:${proxy.port}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              proxy.type.displayName,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          if (proxy.currentIp != null) ...[
                            const SizedBox(width: 8),
                            Text(
                              'IP: ${proxy.currentIp}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                _buildProxyStatusChip(context, proxy.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildProxyInfoItem(
                    context,
                    'آخر اختبار',
                    proxy.lastTested != null
                        ? _formatProxyDateTime(proxy.lastTested!)
                        : 'لم يتم الاختبار',
                    Icons.schedule,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildProxyInfoItem(
                    context,
                    'وقت الاستجابة',
                    proxy.responseTime ?? 'غير محدد',
                    Icons.speed,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildProxyInfoItem(
                    context,
                    'تاريخ الإضافة',
                    _formatProxyDateTime(proxy.createdAt),
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
            if (proxy.lastError != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        proxy.lastError!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _testProxy(context, proxy, provider),
                  icon: const Icon(Icons.play_arrow),
                  label: Text(localizations.testProxy),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showEditProxyDialog(context, proxy),
                  icon: const Icon(Icons.edit),
                  label: Text(localizations.edit),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteProxy(context, proxy, provider),
                  icon: const Icon(Icons.delete),
                  label: Text(localizations.delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProxyStatusChip(BuildContext context, ProxyStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getProxyStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getProxyStatusColor(status)),
      ),
      child: Text(
        status.displayName,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getProxyStatusColor(status),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildProxyInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getProxyStatusColor(ProxyStatus status) {
    switch (status) {
      case ProxyStatus.active:
        return Colors.green;
      case ProxyStatus.failed:
        return Colors.red;
      case ProxyStatus.testing:
        return Colors.blue;
      case ProxyStatus.untested:
        return Colors.orange;
    }
  }

  String _formatProxyDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showAddProxyDialog(BuildContext context) {
    // سيتم تنفيذها لاحقاً
  }

  void _showEditProxyDialog(BuildContext context, ProxyModel proxy) {
    // سيتم تنفيذها لاحقاً
  }

  void _testProxy(BuildContext context, ProxyModel proxy, proxy_provider.ProxyProvider provider) async {
    if (proxy.id == null) return;

    final success = await provider.testProxy(proxy.id!);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'اختبار البروكسي نجح' : 'اختبار البروكسي فشل'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _deleteProxy(BuildContext context, ProxyModel proxy, proxy_provider.ProxyProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف البروكسي'),
        content: Text('هل أنت متأكد من حذف بروكسي "${proxy.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (proxy.id != null) {
                provider.deleteProxy(proxy.id!);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }

  void _testAllProxies(BuildContext context) async {
    final provider = Provider.of<proxy_provider.ProxyProvider>(context, listen: false);
    await provider.testAllProxies();

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم اختبار جميع البروكسيات')),
      );
    }
  }
}

class SchedulerScreen extends StatefulWidget {
  const SchedulerScreen({super.key});

  @override
  State<SchedulerScreen> createState() => _SchedulerScreenState();
}

class _SchedulerScreenState extends State<SchedulerScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ScheduleProvider>(context, listen: false).syncWithApi();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.scheduler),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showAddScheduleDialog(context),
            icon: const Icon(Icons.add),
            tooltip: localizations.addSchedule,
          ),
          IconButton(
            onPressed: () {
              Provider.of<ScheduleProvider>(context, listen: false).syncWithApi();
            },
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
      ),
      body: Consumer<ScheduleProvider>(
        builder: (context, scheduleProvider, child) {
          if (scheduleProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (scheduleProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    scheduleProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => scheduleProvider.syncWithApi(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات الجدولة
              _buildScheduleStats(context, scheduleProvider),

              // قائمة الجدولات
              Expanded(
                child: scheduleProvider.schedules.isEmpty
                    ? _buildEmptyState(context)
                    : _buildSchedulesList(context, scheduleProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildScheduleStats(BuildContext context, ScheduleProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildScheduleStatCard(
              context,
              'نشطة',
              provider.activeSchedulesCount.toString(),
              Icons.play_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildScheduleStatCard(
              context,
              'قادمة',
              provider.upcomingSchedulesCount.toString(),
              Icons.schedule,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildScheduleStatCard(
              context,
              'متأخرة',
              provider.overdueSchedulesCount.toString(),
              Icons.warning,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildScheduleStatCard(
              context,
              'إجمالي التشغيلات',
              provider.totalRunCount.toString(),
              Icons.play_arrow,
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleStatCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد جدولات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء جدولة لأتمتة نشر المنشورات',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddScheduleDialog(context),
            icon: const Icon(Icons.add),
            label: Text(localizations.addSchedule),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulesList(BuildContext context, ScheduleProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.schedules.length,
      itemBuilder: (context, index) {
        final schedule = provider.schedules[index];
        return _buildScheduleCard(context, schedule, provider);
      },
    );
  }

  Widget _buildScheduleCard(BuildContext context, Schedule schedule, ScheduleProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: schedule.isActive ? Colors.green : Colors.grey,
                  child: Icon(
                    schedule.isActive ? Icons.play_arrow : Icons.pause,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        schedule.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        schedule.frequency.displayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: schedule.isActive
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: schedule.isActive ? Colors.green : Colors.grey,
                    ),
                  ),
                  child: Text(
                    schedule.isActive ? 'نشط' : 'متوقف',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: schedule.isActive ? Colors.green : Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildScheduleInfoItem(
                    context,
                    'التشغيل التالي',
                    schedule.nextRun != null
                        ? _formatScheduleDateTime(schedule.nextRun!)
                        : 'غير محدد',
                    Icons.schedule,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildScheduleInfoItem(
                    context,
                    'آخر تشغيل',
                    schedule.lastRun != null
                        ? _formatScheduleDateTime(schedule.lastRun!)
                        : 'لم يتم التشغيل',
                    Icons.history,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildScheduleInfoItem(
                    context,
                    'عدد التشغيلات',
                    schedule.runCount.toString(),
                    Icons.play_arrow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _runScheduleNow(context, schedule, provider),
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('تشغيل الآن'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _toggleSchedule(context, schedule, provider),
                  icon: Icon(schedule.isActive ? Icons.pause : Icons.play_arrow),
                  label: Text(schedule.isActive ? 'إيقاف' : 'تشغيل'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showEditScheduleDialog(context, schedule),
                  icon: const Icon(Icons.edit),
                  label: Text(localizations.edit),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _deleteSchedule(context, schedule, provider),
                  icon: const Icon(Icons.delete),
                  label: Text(localizations.delete),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatScheduleDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showAddScheduleDialog(BuildContext context) {
    _showScheduleDialog(context, null);
  }

  void _showEditScheduleDialog(BuildContext context, Schedule schedule) {
    _showScheduleDialog(context, schedule);
  }

  void _showScheduleDialog(BuildContext context, Schedule? schedule) {
    final localizations = AppLocalizations.of(context)!;
    final isEditing = schedule != null;

    final nameController = TextEditingController(text: schedule?.name ?? '');
    ScheduleFrequency selectedFrequency = schedule?.frequency ?? ScheduleFrequency.daily;
    bool isActive = schedule?.isActive ?? true;
    int selectedPostId = schedule?.postId ?? 0;
    List<int> selectedAccountIds = schedule?.accountIds ?? [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'تعديل الجدولة' : 'إضافة جدولة جديدة'),
        content: SizedBox(
          width: 500,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم الجدولة',
                    prefixIcon: const Icon(Icons.label),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Consumer<PostProvider>(
                  builder: (context, postProvider, child) {
                    return DropdownButtonFormField<int>(
                      value: selectedPostId > 0 ? selectedPostId : null,
                      decoration: InputDecoration(
                        labelText: 'اختر المنشور',
                        prefixIcon: const Icon(Icons.post_add),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: postProvider.posts.map((post) {
                        return DropdownMenuItem<int>(
                          value: post.id,
                          child: Text(
                            post.title,
                            style: const TextStyle(fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (int? newValue) {
                        if (newValue != null) {
                          selectedPostId = newValue;
                        }
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return Column(
                      children: [
                        DropdownButtonFormField<ScheduleFrequency>(
                          value: selectedFrequency,
                          decoration: InputDecoration(
                            labelText: 'تكرار التشغيل',
                            prefixIcon: const Icon(Icons.schedule),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items: ScheduleFrequency.values.map((frequency) {
                            return DropdownMenuItem<ScheduleFrequency>(
                              value: frequency,
                              child: Text(frequency.displayName),
                            );
                          }).toList(),
                          onChanged: (ScheduleFrequency? newValue) {
                            if (newValue != null) {
                              setState(() {
                                selectedFrequency = newValue;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('تفعيل الجدولة'),
                          subtitle: const Text('تشغيل الجدولة تلقائياً'),
                          value: isActive,
                          onChanged: (bool value) {
                            setState(() {
                              isActive = value;
                            });
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () => _saveSchedule(
              context,
              schedule,
              nameController.text,
              selectedPostId,
              selectedFrequency,
              isActive,
            ),
            child: Text(localizations.save),
          ),
        ],
      ),
    );
  }

  void _saveSchedule(BuildContext context, Schedule? existingSchedule, String name,
      int postId, ScheduleFrequency frequency, bool isActive) {
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('اسم الجدولة مطلوب')),
      );
      return;
    }

    if (postId <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب اختيار منشور للجدولة')),
      );
      return;
    }

    final provider = Provider.of<ScheduleProvider>(context, listen: false);

    if (existingSchedule != null) {
      // تعديل جدولة موجودة
      final updatedSchedule = existingSchedule.copyWith(
        name: name,
        postId: postId,
        frequency: frequency,
        isActive: isActive,
        nextRun: _calculateNextRun(frequency),
      );
      provider.updateSchedule(updatedSchedule);
    } else {
      // إضافة جدولة جديدة
      final newSchedule = Schedule(
        name: name,
        postId: postId,
        startDate: DateTime.now(),
        frequency: frequency,
        isActive: isActive,
        nextRun: _calculateNextRun(frequency),
        runCount: 0,
      );
      provider.addSchedule(newSchedule);
    }

    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(existingSchedule != null ? 'تم تحديث الجدولة بنجاح' : 'تم إضافة الجدولة بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  DateTime _calculateNextRun(ScheduleFrequency frequency) {
    final now = DateTime.now();
    switch (frequency) {
      case ScheduleFrequency.once:
        return now.add(const Duration(minutes: 5)); // تشغيل بعد 5 دقائق
      case ScheduleFrequency.daily:
        return DateTime(now.year, now.month, now.day + 1, 9, 0); // 9 AM tomorrow
      case ScheduleFrequency.weekly:
        return now.add(const Duration(days: 7));
      case ScheduleFrequency.monthly:
        return DateTime(now.year, now.month + 1, now.day, now.hour, now.minute);
      case ScheduleFrequency.custom:
        return now.add(const Duration(hours: 1)); // افتراضي للمخصص
    }
  }

  void _runScheduleNow(BuildContext context, Schedule schedule, ScheduleProvider provider) async {
    if (schedule.id == null) return;

    await provider.runScheduleNow(schedule.id!);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تشغيل الجدولة بنجاح')),
      );
    }
  }

  void _toggleSchedule(BuildContext context, Schedule schedule, ScheduleProvider provider) async {
    if (schedule.id == null) return;

    await provider.toggleSchedule(schedule.id!);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(schedule.isActive ? 'تم إيقاف الجدولة' : 'تم تشغيل الجدولة'),
        ),
      );
    }
  }

  void _deleteSchedule(BuildContext context, Schedule schedule, ScheduleProvider provider) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الجدولة'),
        content: Text('هل أنت متأكد من حذف جدولة "${schedule.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (schedule.id != null) {
                provider.deleteSchedule(schedule.id!);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}

class LogsScreen extends StatefulWidget {
  const LogsScreen({super.key});

  @override
  State<LogsScreen> createState() => _LogsScreenState();
}

class _LogsScreenState extends State<LogsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<LogProvider>(context, listen: false).syncWithApi();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.logs),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => _showFilterDialog(context),
            icon: const Icon(Icons.filter_list),
            tooltip: localizations.filter,
          ),
          IconButton(
            onPressed: () => _clearLogs(context),
            icon: const Icon(Icons.clear_all),
            tooltip: localizations.clearLogs,
          ),
          IconButton(
            onPressed: () {
              Provider.of<LogProvider>(context, listen: false).syncWithApi();
            },
            icon: const Icon(Icons.refresh),
            tooltip: localizations.refresh,
          ),
        ],
      ),
      body: Consumer<LogProvider>(
        builder: (context, logProvider, child) {
          if (logProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (logProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    logProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => logProvider.syncWithApi(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // إحصائيات السجلات
              _buildLogStats(context, logProvider),

              // فلاتر نشطة
              if (_hasActiveFilters(logProvider))
                _buildActiveFilters(context, logProvider),

              // قائمة السجلات
              Expanded(
                child: logProvider.logs.isEmpty
                    ? _buildEmptyState(context)
                    : _buildLogsList(context, logProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLogStats(BuildContext context, LogProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildLogStatCard(
              context,
              'أخطاء',
              provider.errorLogsCount.toString(),
              Icons.error,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildLogStatCard(
              context,
              'تحذيرات',
              provider.warningLogsCount.toString(),
              Icons.warning,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildLogStatCard(
              context,
              'معلومات',
              provider.infoLogsCount.toString(),
              Icons.info,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildLogStatCard(
              context,
              'إجمالي السجلات',
              provider.totalLogs.toString(),
              Icons.list_alt,
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogStatCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  bool _hasActiveFilters(LogProvider provider) {
    return provider.selectedLevel != null ||
           provider.selectedCategory != null ||
           provider.selectedAccountId != null ||
           provider.selectedPostId != null ||
           provider.startDate != null ||
           provider.endDate != null ||
           (provider.searchQuery != null && provider.searchQuery!.isNotEmpty);
  }

  Widget _buildActiveFilters(BuildContext context, LogProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (provider.selectedLevel != null)
            _buildFilterChip(
              context,
              'المستوى: ${provider.selectedLevel!.displayName}',
              () => provider.setLevelFilter(null),
            ),
          if (provider.selectedCategory != null)
            _buildFilterChip(
              context,
              'الفئة: ${provider.selectedCategory!.displayName}',
              () => provider.setCategoryFilter(null),
            ),
          if (provider.searchQuery != null && provider.searchQuery!.isNotEmpty)
            _buildFilterChip(
              context,
              'البحث: ${provider.searchQuery}',
              () => provider.setSearchQuery(null),
            ),
          TextButton.icon(
            onPressed: () => provider.clearFilters(),
            icon: const Icon(Icons.clear_all, size: 16),
            label: const Text('مسح الفلاتر'),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onRemove,
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.list_alt_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد سجلات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر السجلات هنا عند حدوث أنشطة في النظام',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList(BuildContext context, LogProvider provider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.logs.length,
      itemBuilder: (context, index) {
        final log = provider.logs[index];
        return _buildLogCard(context, log);
      },
    );
  }

  Widget _buildLogCard(BuildContext context, LogEntry log) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getLogLevelColor(log.level),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getLogLevelColor(log.level).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: _getLogLevelColor(log.level)),
                  ),
                  child: Text(
                    log.level.displayName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getLogLevelColor(log.level),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    log.category.displayName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  _formatLogDateTime(log.timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              log.message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            if (log.details != null) ...[
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  log.details.toString(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ],
            if (log.accountId != null || log.postId != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (log.accountId != null) ...[
                    Icon(
                      Icons.person,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'حساب: ${log.accountId}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (log.postId != null) ...[
                    Icon(
                      Icons.post_add,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'منشور: ${log.postId}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.error:
        return Colors.red;
      case LogLevel.warn:
        return Colors.orange;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.debug:
        return Colors.grey;
    }
  }

  String _formatLogDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showFilterDialog(BuildContext context) {
    // سيتم تنفيذها لاحقاً
  }

  void _clearLogs(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.clearLogs),
        content: const Text('هل أنت متأكد من مسح جميع السجلات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Provider.of<LogProvider>(context, listen: false).clearLogs();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}
