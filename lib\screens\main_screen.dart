import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../utils/app_localizations.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const AccountsScreen(),
    const PostsScreen(),
    const ProxiesScreen(),
    const SchedulerScreen(),
    const LogsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: appProvider.isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: Row(
          children: [
            // الشريط الجانبي
            NavigationRail(
              selectedIndex: _selectedIndex,
              onDestinationSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              extended: true,
              minExtendedWidth: 200,
              backgroundColor: Theme.of(context).colorScheme.surface,
              elevation: 2,
              leading: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.store,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      localizations.appTitle,
                      style: Theme.of(context).textTheme.titleSmall,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              trailing: Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر تبديل اللغة
                        IconButton(
                          onPressed: () => appProvider.toggleLanguage(),
                          icon: const Icon(Icons.language),
                          tooltip: localizations.language,
                        ),
                        const SizedBox(height: 8),
                        // زر الإعدادات
                        IconButton(
                          onPressed: () => _showSettingsDialog(context),
                          icon: const Icon(Icons.settings),
                          tooltip: localizations.settings,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              destinations: [
                NavigationRailDestination(
                  icon: const Icon(Icons.dashboard),
                  selectedIcon: const Icon(Icons.dashboard),
                  label: Text(localizations.dashboard),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.account_circle),
                  selectedIcon: const Icon(Icons.account_circle),
                  label: Text(localizations.accounts),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.post_add),
                  selectedIcon: const Icon(Icons.post_add),
                  label: Text(localizations.posts),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.security),
                  selectedIcon: const Icon(Icons.security),
                  label: Text(localizations.proxies),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.schedule),
                  selectedIcon: const Icon(Icons.schedule),
                  label: Text(localizations.scheduler),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.list_alt),
                  selectedIcon: const Icon(Icons.list_alt),
                  label: Text(localizations.logs),
                ),
              ],
            ),
            
            // خط فاصل
            const VerticalDivider(thickness: 1, width: 1),
            
            // المحتوى الرئيسي
            Expanded(
              child: _screens[_selectedIndex],
            ),
          ],
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.settings),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // إعداد المظهر
            ListTile(
              leading: const Icon(Icons.palette),
              title: Text(localizations.theme),
              trailing: DropdownButton<ThemeMode>(
                value: appProvider.themeMode,
                onChanged: (ThemeMode? mode) {
                  if (mode != null) {
                    appProvider.setThemeMode(mode);
                  }
                },
                items: [
                  DropdownMenuItem(
                    value: ThemeMode.system,
                    child: Text(localizations.systemTheme),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.light,
                    child: Text(localizations.lightTheme),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.dark,
                    child: Text(localizations.darkTheme),
                  ),
                ],
              ),
            ),
            
            // إعداد اللغة
            ListTile(
              leading: const Icon(Icons.language),
              title: Text(localizations.language),
              trailing: DropdownButton<String>(
                value: appProvider.locale.languageCode,
                onChanged: (String? languageCode) {
                  if (languageCode != null) {
                    final locale = languageCode == 'ar' 
                        ? const Locale('ar', 'SA')
                        : const Locale('en', 'US');
                    appProvider.setLocale(locale);
                  }
                },
                items: [
                  DropdownMenuItem(
                    value: 'ar',
                    child: Text(localizations.arabic),
                  ),
                  DropdownMenuItem(
                    value: 'en',
                    child: Text(localizations.english),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
        ],
      ),
    );
  }
}

// شاشات مؤقتة بسيطة
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.dashboard),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('لوحة التحكم - قيد التطوير'),
      ),
    );
  }
}

class AccountsScreen extends StatelessWidget {
  const AccountsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.accounts),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('إدارة الحسابات - قيد التطوير'),
      ),
    );
  }
}

class PostsScreen extends StatelessWidget {
  const PostsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.posts),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('إدارة المنشورات - قيد التطوير'),
      ),
    );
  }
}

class ProxiesScreen extends StatelessWidget {
  const ProxiesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.proxies),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('إدارة البروكسيات - قيد التطوير'),
      ),
    );
  }
}

class SchedulerScreen extends StatelessWidget {
  const SchedulerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.scheduler),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('إدارة الجدولة - قيد التطوير'),
      ),
    );
  }
}

class LogsScreen extends StatelessWidget {
  const LogsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.logs),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text('عرض السجلات - قيد التطوير'),
      ),
    );
  }
}
