import { PuppeteerExtraPlugin } from 'puppeteer-extra-plugin';
import { <PERSON><PERSON><PERSON>, Frame, <PERSON> } from 'puppeteer';
import * as types from './types';
export declare const BuiltinSolutionProviders: types.SolutionProvider[];
/**
 * A puppeteer-extra plugin to automatically detect and solve reCAPTCHAs.
 * @noInheritDoc
 */
export declare class PuppeteerExtraPluginRecaptcha extends PuppeteerExtraPlugin {
    private contentScriptDebug;
    constructor(opts: Partial<types.PluginOptions>);
    get name(): string;
    get defaults(): types.PluginOptions;
    get opts(): types.PluginOptions;
    get contentScriptOpts(): types.ContentScriptOpts;
    /** An optional global window object we use for contentscript debug logging */
    private debugBindingName;
    private _generateContentScript;
    /** Based on the user defined options we may want to filter out certain captchas (inactive, etc) */
    private _filterRecaptchas;
    findRecaptchas(page: Page | Frame): Promise<types.FindRecaptchasResult>;
    getRecaptchaSolutions(captchas: types.CaptchaInfo[], provider?: types.SolutionProvider): Promise<any>;
    enterRecaptchaSolutions(page: Page | Frame, solutions: types.CaptchaSolution[]): Promise<types.EnterRecaptchaSolutionsResult>;
    solveRecaptchas(page: Page | Frame): Promise<types.SolveRecaptchasResult>;
    private _addCustomMethods;
    onPageCreated(page: Page): Promise<void>;
    /** Add additions to already existing pages and frames */
    onBrowser(browser: Browser): Promise<void>;
}
/** Default export, PuppeteerExtraPluginRecaptcha  */
declare const defaultExport: (options?: Partial<types.PluginOptions>) => PuppeteerExtraPluginRecaptcha;
export default defaultExport;
