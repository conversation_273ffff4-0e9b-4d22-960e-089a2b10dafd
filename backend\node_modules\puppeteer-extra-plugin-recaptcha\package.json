{"name": "puppeteer-extra-plugin-recaptcha", "version": "3.6.8", "description": "A puppeteer-extra plugin to solve reCAPTCHAs and hCaptchas automatically.", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "dist/index.d.ts", "files": ["dist"], "repository": "berstend/puppeteer-extra", "homepage": "https://github.com/berstend/puppeteer-extra/tree/master/packages/puppeteer-extra-plugin-recaptcha", "author": "be<PERSON><PERSON>", "license": "MIT", "scripts": {"clean": "rimraf dist/*", "tscheck": "tsc --pretty --noEmit", "prebuild": "run-s clean", "build": "run-s build:tsc build:rollup ambient-dts", "build:tsc": "tsc --project tsconfig.json --module commonjs", "build:rollup": "rollup -c rollup.config.ts", "docs": "node -e 0", "predocs2": "rim<PERSON>f docs/*", "docs2": "typedoc --module commonjs --readme none --target ES6 --theme markdown --excludeNotExported --excludeExternals --excludePrivate --out docs --mode file src/index.ts", "test": "ava -v --config ava.config-ts.js", "pretest-ci": "run-s build", "test-ci": "ava --fail-fast --concurrency 2 -v", "ambient-dts": "run-s ambient-dts-copy ambient-dts-fix-path", "ambient-dts-copy": "copyfiles -u 1 \"src/**/*.d.ts\" dist", "ambient-dts-fix-path": "replace-in-files --string='/// <reference path=\"../src/' --replacement='/// <reference path=\"../dist/' 'dist/**/*.d.ts'"}, "engines": {"node": ">=9.11.2"}, "prettier": {"printWidth": 80, "semi": false, "singleQuote": true}, "keywords": ["puppeteer", "puppeteer-extra", "puppeteer-extra-plugin", "recaptcha", "h<PERSON><PERSON>a", "<PERSON><PERSON>a", "2captcha"], "devDependencies": {"@types/debug": "^4.1.5", "@types/node": "14.17.6", "@types/puppeteer": "*", "ava": "2.4.0", "copyfiles": "^2.1.1", "npm-run-all": "^4.1.5", "puppeteer": "9", "puppeteer-extra": "^3.3.6", "replace-in-files-cli": "^0.3.1", "rimraf": "^3.0.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-typescript2": "^0.25.2", "ts-node": "^8.5.4", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "4.4.3"}, "dependencies": {"debug": "^4.1.1", "merge-deep": "^3.0.2", "puppeteer-extra-plugin": "^3.2.3"}, "peerDependencies": {"playwright-extra": "*", "puppeteer-extra": "*"}, "peerDependenciesMeta": {"puppeteer-extra": {"optional": true}, "playwright-extra": {"optional": true}}, "gitHead": "39248f1f5deeb21b1e7eb6ae07b8ef73f1231ab9"}