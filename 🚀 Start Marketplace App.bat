@echo off
title Facebook Marketplace Automation - Launcher
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    ███████╗██████╗     ███╗   ███╗ █████╗ ██████╗ ██╗  ██╗   ║
echo ║    ██╔════╝██╔══██╗    ████╗ ████║██╔══██╗██╔══██╗██║ ██╔╝   ║
echo ║    █████╗  ██████╔╝    ██╔████╔██║███████║██████╔╝█████╔╝    ║
echo ║    ██╔══╝  ██╔══██╗    ██║╚██╔╝██║██╔══██║██╔══██╗██╔═██╗    ║
echo ║    ██║     ██████╔╝    ██║ ╚═╝ ██║██║  ██║██║  ██║██║  ██╗   ║
echo ║    ╚═╝     ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝   ║
echo ║                                                              ║
echo ║              Facebook Marketplace Automation                 ║
echo ║                   Professional Launcher                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting Facebook Marketplace Automation...
echo.

REM Check if PowerShell script exists
if not exist "launcher.ps1" (
    echo ❌ PowerShell launcher script not found!
    echo Please ensure launcher.ps1 is in the same directory.
    pause
    exit /b 1
)

echo 🔧 Launching with PowerShell...
powershell -ExecutionPolicy Bypass -File "launcher.ps1" -Both

echo.
echo ✅ Launcher completed. Check the opened windows for your applications.
echo.
pause
