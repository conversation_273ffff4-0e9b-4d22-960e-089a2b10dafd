const express = require('express');
const router = express.Router();
const cron = require('node-cron');

// In-memory storage for demo
let schedules = [];
let scheduleIdCounter = 1;
let activeCronJobs = new Map();

// GET /api/scheduler - Get all schedules
router.get('/', (req, res) => {
    try {
        res.json(schedules);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/scheduler - Create new schedule
router.post('/', (req, res) => {
    try {
        const { 
            name, 
            postId, 
            accountIds, 
            startDate, 
            endDate, 
            frequency, 
            interval, 
            randomDelay,
            isActive 
        } = req.body;
        
        if (!name || !postId || !accountIds || !startDate || !frequency) {
            return res.status(400).json({ 
                error: 'Name, postId, accountIds, startDate, and frequency are required' 
            });
        }
        
        const newSchedule = {
            id: scheduleIdCounter++,
            name,
            postId: parseInt(postId),
            accountIds: Array.isArray(accountIds) ? accountIds : [accountIds],
            startDate,
            endDate: endDate || null,
            frequency, // 'once', 'daily', 'weekly', 'monthly', 'custom'
            interval: interval || 1,
            randomDelay: randomDelay || 0, // in minutes
            isActive: isActive !== false,
            nextRun: calculateNextRun(startDate, frequency, interval),
            lastRun: null,
            runCount: 0,
            createdAt: new Date().toISOString()
        };
        
        schedules.push(newSchedule);
        
        // Start the cron job if active
        if (newSchedule.isActive) {
            startCronJob(newSchedule);
        }
        
        res.status(201).json(newSchedule);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// PUT /api/scheduler/:id - Update schedule
router.put('/:id', (req, res) => {
    try {
        const scheduleId = parseInt(req.params.id);
        const scheduleIndex = schedules.findIndex(schedule => schedule.id === scheduleId);
        
        if (scheduleIndex === -1) {
            return res.status(404).json({ error: 'Schedule not found' });
        }
        
        const schedule = schedules[scheduleIndex];
        const { 
            name, 
            postId, 
            accountIds, 
            startDate, 
            endDate, 
            frequency, 
            interval, 
            randomDelay,
            isActive 
        } = req.body;
        
        // Stop existing cron job
        stopCronJob(scheduleId);
        
        // Update fields
        if (name) schedule.name = name;
        if (postId) schedule.postId = parseInt(postId);
        if (accountIds) schedule.accountIds = Array.isArray(accountIds) ? accountIds : [accountIds];
        if (startDate) schedule.startDate = startDate;
        if (endDate !== undefined) schedule.endDate = endDate;
        if (frequency) schedule.frequency = frequency;
        if (interval) schedule.interval = interval;
        if (randomDelay !== undefined) schedule.randomDelay = randomDelay;
        if (isActive !== undefined) schedule.isActive = isActive;
        
        // Recalculate next run
        schedule.nextRun = calculateNextRun(schedule.startDate, schedule.frequency, schedule.interval);
        
        // Start new cron job if active
        if (schedule.isActive) {
            startCronJob(schedule);
        }
        
        res.json(schedule);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// DELETE /api/scheduler/:id - Delete schedule
router.delete('/:id', (req, res) => {
    try {
        const scheduleId = parseInt(req.params.id);
        const scheduleIndex = schedules.findIndex(schedule => schedule.id === scheduleId);
        
        if (scheduleIndex === -1) {
            return res.status(404).json({ error: 'Schedule not found' });
        }
        
        // Stop cron job
        stopCronJob(scheduleId);
        
        schedules.splice(scheduleIndex, 1);
        res.json({ message: 'Schedule deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/scheduler/:id/toggle - Toggle schedule active status
router.post('/:id/toggle', (req, res) => {
    try {
        const scheduleId = parseInt(req.params.id);
        const schedule = schedules.find(s => s.id === scheduleId);
        
        if (!schedule) {
            return res.status(404).json({ error: 'Schedule not found' });
        }
        
        schedule.isActive = !schedule.isActive;
        
        if (schedule.isActive) {
            startCronJob(schedule);
        } else {
            stopCronJob(scheduleId);
        }
        
        res.json(schedule);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/scheduler/:id/run-now - Run schedule immediately
router.post('/:id/run-now', async (req, res) => {
    try {
        const scheduleId = parseInt(req.params.id);
        const schedule = schedules.find(s => s.id === scheduleId);
        
        if (!schedule) {
            return res.status(404).json({ error: 'Schedule not found' });
        }
        
        // Execute the scheduled task
        await executeScheduledTask(schedule);
        
        res.json({ message: 'Schedule executed successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Helper functions
function calculateNextRun(startDate, frequency, interval) {
    const start = new Date(startDate);
    const now = new Date();
    
    if (start > now) {
        return start.toISOString();
    }
    
    let nextRun = new Date(start);
    
    switch (frequency) {
        case 'once':
            return start > now ? start.toISOString() : null;
        case 'daily':
            while (nextRun <= now) {
                nextRun.setDate(nextRun.getDate() + interval);
            }
            break;
        case 'weekly':
            while (nextRun <= now) {
                nextRun.setDate(nextRun.getDate() + (7 * interval));
            }
            break;
        case 'monthly':
            while (nextRun <= now) {
                nextRun.setMonth(nextRun.getMonth() + interval);
            }
            break;
        default:
            return null;
    }
    
    return nextRun.toISOString();
}

function startCronJob(schedule) {
    const cronExpression = getCronExpression(schedule);
    if (!cronExpression) return;
    
    const task = cron.schedule(cronExpression, async () => {
        await executeScheduledTask(schedule);
    }, {
        scheduled: false
    });
    
    task.start();
    activeCronJobs.set(schedule.id, task);
}

function stopCronJob(scheduleId) {
    const task = activeCronJobs.get(scheduleId);
    if (task) {
        task.stop();
        activeCronJobs.delete(scheduleId);
    }
}

function getCronExpression(schedule) {
    const startDate = new Date(schedule.startDate);
    const minute = startDate.getMinutes();
    const hour = startDate.getHours();
    const dayOfMonth = startDate.getDate();
    const dayOfWeek = startDate.getDay();
    
    switch (schedule.frequency) {
        case 'daily':
            return `${minute} ${hour} */${schedule.interval} * *`;
        case 'weekly':
            return `${minute} ${hour} * * ${dayOfWeek}`;
        case 'monthly':
            return `${minute} ${hour} ${dayOfMonth} */${schedule.interval} *`;
        default:
            return null;
    }
}

async function executeScheduledTask(schedule) {
    try {
        console.log(`Executing scheduled task: ${schedule.name}`);
        
        // Update schedule stats
        schedule.lastRun = new Date().toISOString();
        schedule.runCount++;
        schedule.nextRun = calculateNextRun(
            schedule.startDate, 
            schedule.frequency, 
            schedule.interval
        );
        
        // TODO: Implement actual post publishing logic
        // This would integrate with the Playwright automation
        
        // For now, just log the execution
        console.log(`Published post ${schedule.postId} to accounts ${schedule.accountIds.join(', ')}`);
        
        // If it's a one-time schedule and it has run, deactivate it
        if (schedule.frequency === 'once') {
            schedule.isActive = false;
            stopCronJob(schedule.id);
        }
        
    } catch (error) {
        console.error(`Error executing scheduled task ${schedule.name}:`, error);
    }
}

module.exports = router;
