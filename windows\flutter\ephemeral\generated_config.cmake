# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\scr\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\Marketplace -app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\scr\\flutter"
  "PROJECT_DIR=E:\\Marketplace -app"
  "FLUTTER_ROOT=C:\\scr\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\Marketplace -app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\Marketplace -app"
  "FLUTTER_TARGET=lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=true"
  "PACKAGE_CONFIG=E:\\Marketplace -app\\.dart_tool\\package_config.json"
)
