// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ScheduleAdapter extends TypeAdapter<Schedule> {
  @override
  final int typeId = 8;

  @override
  Schedule read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Schedule(
      id: fields[0] as int?,
      name: fields[1] as String,
      postId: fields[2] as int,
      accountIds: (fields[3] as List?)?.cast<int>(),
      startDate: fields[4] as DateTime,
      endDate: fields[5] as DateTime?,
      frequency: fields[6] as ScheduleFrequency,
      interval: fields[7] as int,
      randomDelay: fields[8] as int,
      isActive: fields[9] as bool,
      nextRun: fields[10] as DateTime?,
      lastRun: fields[11] as DateTime?,
      runCount: fields[12] as int,
      createdAt: fields[13] as DateTime?,
      settings: (fields[14] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Schedule obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.postId)
      ..writeByte(3)
      ..write(obj.accountIds)
      ..writeByte(4)
      ..write(obj.startDate)
      ..writeByte(5)
      ..write(obj.endDate)
      ..writeByte(6)
      ..write(obj.frequency)
      ..writeByte(7)
      ..write(obj.interval)
      ..writeByte(8)
      ..write(obj.randomDelay)
      ..writeByte(9)
      ..write(obj.isActive)
      ..writeByte(10)
      ..write(obj.nextRun)
      ..writeByte(11)
      ..write(obj.lastRun)
      ..writeByte(12)
      ..write(obj.runCount)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.settings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ScheduleFrequencyAdapter extends TypeAdapter<ScheduleFrequency> {
  @override
  final int typeId = 9;

  @override
  ScheduleFrequency read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ScheduleFrequency.once;
      case 1:
        return ScheduleFrequency.daily;
      case 2:
        return ScheduleFrequency.weekly;
      case 3:
        return ScheduleFrequency.monthly;
      case 4:
        return ScheduleFrequency.custom;
      default:
        return ScheduleFrequency.once;
    }
  }

  @override
  void write(BinaryWriter writer, ScheduleFrequency obj) {
    switch (obj) {
      case ScheduleFrequency.once:
        writer.writeByte(0);
        break;
      case ScheduleFrequency.daily:
        writer.writeByte(1);
        break;
      case ScheduleFrequency.weekly:
        writer.writeByte(2);
        break;
      case ScheduleFrequency.monthly:
        writer.writeByte(3);
        break;
      case ScheduleFrequency.custom:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleFrequencyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
