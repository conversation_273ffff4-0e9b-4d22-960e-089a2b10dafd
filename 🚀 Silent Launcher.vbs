' Facebook Marketplace Automation - Silent Launcher
' This script launches the application without showing the command prompt window

Set objShell = CreateObject("WScript.Shell")

' Get the current directory
strCurrentDir = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)

' Change to the application directory
objShell.CurrentDirectory = strCurrentDir

' Launch the PowerShell script silently
objShell.Run "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File ""start-marketplace-app.ps1"" -Both", 0, False

' Show a notification that the app is starting
objShell.Popup "🚀 Facebook Marketplace Automation is starting..." & vbCrLf & vbCrLf & "Please wait for the applications to load.", 3, "Marketplace Automation", 64
