{"version": 3, "file": "2captcha-api.js", "sourceRoot": "", "sources": ["../../src/provider/2captcha-api.ts"], "names": [], "mappings": ";AAAA,mEAAmE;AACnE,mCAAmC;;;AAEnC,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;AAC5B,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;AACxB,IAAI,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;AAExC,IAAI,MAAM,CAAA;AACV,IAAI,QAAQ,GAAG,6BAA6B,CAAA;AAC5C,IAAI,SAAS,GAAG,8BAA8B,CAAA;AAC9C,IAAI,SAAS,GAAG,QAAQ,CAAA;AACxB,IAAI,OAAO,GAAG,MAAM,CAAA;AAEpB,IAAI,cAAc,GAAG;IACnB,eAAe,EAAE,IAAI;IACrB,OAAO,EAAE,CAAC;CACX,CAAA;AAED,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;IACxD,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAA;IAClE,IAAI,UAAU,GAAG,WAAW,CAAC;QAC3B,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CACjC,SAAS;YACP,sBAAsB;YACtB,OAAO;YACP,OAAO;YACP,MAAM;YACN,MAAM;YACN,SAAS,CACZ,CAAA;QACD,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;YAChE,IAAI,IAAI,GAAG,EAAE,CAAA;YAEb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;gBAChC,IAAI,IAAI,KAAK,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;gBACjB,IAAI,IAAI,KAAK,kBAAkB,EAAE;oBAC/B,OAAM;iBACP;gBAED,aAAa,CAAC,UAAU,CAAC,CAAA;gBAEzB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACtB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,OAAO;iBAC5B;qBAAM;oBACL,QAAQ,CACN,IAAI,EACJ;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;qBAChB,EACD,OAAO,CACR,CAAA;iBACF;gBACD,QAAQ,GAAG,cAAY,CAAC,CAAA,CAAC,+GAA+G;YAC1I,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;YAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;YACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;QACb,CAAC,CAAC,CAAA;QACF,OAAO,CAAC,GAAG,EAAE,CAAA;IACf,CAAC,EAAE,OAAO,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,CAAC,CAAA;AAC/D,CAAC;AAEM,MAAM,SAAS,GAAG,UAAS,GAAG;IACnC,MAAM,GAAG,GAAG,CAAA;AACd,CAAC,CAAA;AAFY,QAAA,SAAS,aAErB;AAEM,MAAM,MAAM,GAAG,UAAS,MAAM,EAAE,OAAO,EAAE,QAAQ;IACtD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,OAAO,CAAA;QAClB,OAAO,GAAG,cAAc,CAAA;KACzB;IACD,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC7C,mBAAmB,CAAC,MAAM,GAAG,MAAM,CAAA;IAEnC,IAAI,QAAQ,GAAG;QACb,MAAM,EAAE,SAAS;QACjB,GAAG,EAAE,MAAM;QACX,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE,MAAM;KACb,CAAA;IAED,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IAE1C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;QAChE,IAAI,IAAI,GAAG,EAAE,CAAA;QAEb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;YAChC,IAAI,IAAI,KAAK,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACtB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;aAC3B;YAED,WAAW,CACT,MAAM,CAAC,CAAC,CAAC,EACT,OAAO,EACP,UAAS,KAAK;gBACZ,IAAI,yBAAyB,GAAG,QAAQ,CAAA;gBAExC,IAAA,cAAM,EAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBAEtB,IAAI,KAAK,EAAE;oBACT,OAAO,yBAAyB,CAAC,gBAAgB,CAAC,CAAA;iBACnD;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACzB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAA;iBAC9C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE;oBAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAA;oBAC/C,IAAA,cAAM,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;iBACvC;qBAAM;oBACL,yBAAyB,CAAC,+BAA+B,CAAC,CAAA;iBAC3D;YACH,CAAC,EACD,QAAQ,CACT,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;QAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;QACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;IACb,CAAC,CAAC,CAAA;IAEF,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IACvB,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AA/DY,QAAA,MAAM,UA+DlB;AAEM,MAAM,eAAe,GAAG,UAC7B,aAAa,EACb,OAAO,EACP,OAAO,EACP,SAAS,EACT,OAAO,EACP,QAAQ;IAER,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,OAAO,CAAA;QAClB,OAAO,GAAG,cAAc,CAAA;KACzB;IACD,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC7C,mBAAmB,CAAC,MAAM,GAAG,MAAM,CAAA;IAEnC,IAAI,QAAQ,mBACV,MAAM,EAAE,aAAa,EACrB,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,OAAO;QAChB,sBAAsB;QACtB,OAAO,EAAE,OAAO,IACb,SAAS,CACb,CAAA;IACD,IAAI,aAAa,KAAK,eAAe,EAAE;QACrC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAA;KAC7B;IACD,IAAI,aAAa,KAAK,UAAU,EAAE;QAChC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAA;KAC3B;IAED,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IAE1C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;QAChE,IAAI,IAAI,GAAG,EAAE,CAAA;QAEb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;YAChC,IAAI,IAAI,KAAK,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACtB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;aAC3B;YAED,WAAW,CACT,MAAM,CAAC,CAAC,CAAC,EACT,OAAO,EACP,UAAS,KAAK;gBACZ,IAAI,yBAAyB,GAAG,QAAQ,CAAA;gBAExC,IAAA,cAAM,EAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBAEtB,IAAI,KAAK,EAAE;oBACT,OAAO,yBAAyB,CAAC,gBAAgB,CAAC,CAAA;iBACnD;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACzB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAA;iBAC9C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE;oBAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAA;oBAC/C,IAAA,uBAAe,EACb,aAAa,EACb,OAAO,EACP,OAAO,EACP,SAAS,EACT,IAAI,CAAC,OAAO,EACZ,QAAQ,CACT,CAAA;iBACF;qBAAM;oBACL,yBAAyB,CAAC,+BAA+B,CAAC,CAAA;iBAC3D;YACH,CAAC,EACD,QAAQ,CACT,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;QAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;QACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;IACb,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IACvB,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AApFY,QAAA,eAAe,mBAoF3B;AAEM,MAAM,SAAS,GAAG,UAAS,GAAG,EAAE,OAAO,EAAE,QAAQ;IACtD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,OAAO,CAAA;QAClB,OAAO,GAAG,cAAc,CAAA;KACzB;IAED,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,QAAQ;QACpD,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAE9B,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;YAChC,IAAI,IAAI,KAAK,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,IAAA,cAAM,EAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;QACjC,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;QAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;QACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;IACb,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AAzBY,QAAA,SAAS,aAyBrB;AAEM,MAAM,sBAAsB,GAAG,UAAS,IAAI,EAAE,OAAO,EAAE,QAAQ;IACpE,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,OAAO,CAAA;QAClB,OAAO,GAAG,cAAc,CAAA;KACzB;IACD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;IAC3C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,QAAQ,CAAC,0BAA0B,CAAC,CAAA;IACrE,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;IACxB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACnC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACnC,SAAS,GAAG,mDAAmD,GAAG,SAAS,CAAA;IAE3E,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IAE9C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;QAChE,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;YAChC,IAAI,IAAI,KAAK,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;gBAAE,OAAO,QAAQ,CAAC,wBAAwB,CAAC,CAAA;YAC/D,IAAI,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;YAC/B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,QAAQ,CAAC,wBAAwB,CAAC,CAAA;YAErE,IAAA,iBAAS,EACP,+CAA+C,GAAG,SAAS,EAC3D,OAAO,EACP,UAAS,KAAK,EAAE,MAAM,EAAE,OAAO;gBAC7B,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,SAAS,GAAG,SAAS,CAAA;iBAC7B;gBACD,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;YAClC,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AAvCY,QAAA,sBAAsB,0BAuClC;AAEM,MAAM,MAAM,GAAG,UAAS,SAAS;IACtC,IAAI,SAAS,GACX,SAAS;QACT,4BAA4B;QAC5B,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAA;IACX,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IAElC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,QAAQ;QACpD,gBAAgB;QAChB,wCAAwC;QACxC,kBAAkB;QAClB,KAAK;QACL,oCAAoC;IACtC,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AAnBY,QAAA,MAAM,UAmBlB"}