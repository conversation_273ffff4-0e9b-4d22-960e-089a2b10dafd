import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class ScheduleProvider extends ChangeNotifier {
  final ApiService _apiService;
  
  List<Schedule> _schedules = [];
  bool _isLoading = false;
  String? _error;

  List<Schedule> get schedules => _schedules;
  bool get isLoading => _isLoading;
  String? get error => _error;

  ScheduleProvider(this._apiService) {
    _loadSchedules();
  }

  Future<void> _loadSchedules() async {
    _setLoading(true);
    try {
      // تحميل من التخزين المحلي أولاً
      _schedules = StorageService.getAllSchedules();
      notifyListeners();

      // ثم المزامنة مع API
      await syncWithApi();
    } catch (e) {
      _setError('فشل في تحميل الجدولة: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithApi() async {
    try {
      final apiSchedules = await _apiService.getSchedules();
      _schedules = apiSchedules;
      
      // حفظ في التخزين المحلي
      for (final schedule in _schedules) {
        await StorageService.saveSchedule(schedule);
      }
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مزامنة الجدولة: $e');
    }
  }

  Future<void> addSchedule(Schedule schedule) async {
    _setLoading(true);
    try {
      final newSchedule = await _apiService.createSchedule(schedule);
      _schedules.add(newSchedule);
      await StorageService.saveSchedule(newSchedule);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في إضافة الجدولة: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateSchedule(Schedule schedule) async {
    _setLoading(true);
    try {
      final updatedSchedule = await _apiService.updateSchedule(schedule);
      final index = _schedules.indexWhere((s) => s.id == schedule.id);
      if (index != -1) {
        _schedules[index] = updatedSchedule;
        await StorageService.saveSchedule(updatedSchedule);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل في تحديث الجدولة: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteSchedule(int scheduleId) async {
    _setLoading(true);
    try {
      await _apiService.deleteSchedule(scheduleId);
      _schedules.removeWhere((s) => s.id == scheduleId);
      await StorageService.deleteSchedule(scheduleId);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في حذف الجدولة: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> toggleSchedule(int scheduleId) async {
    try {
      final updatedSchedule = await _apiService.toggleSchedule(scheduleId);
      final index = _schedules.indexWhere((s) => s.id == scheduleId);
      if (index != -1) {
        _schedules[index] = updatedSchedule;
        await StorageService.saveSchedule(updatedSchedule);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل في تبديل حالة الجدولة: $e');
    }
  }

  Future<void> runScheduleNow(int scheduleId) async {
    try {
      // تحديث حالة الجدولة محلياً
      final scheduleIndex = _schedules.indexWhere((s) => s.id == scheduleId);
      if (scheduleIndex != -1) {
        final schedule = _schedules[scheduleIndex];
        final updatedSchedule = schedule.copyWith(
          lastRun: DateTime.now(),
          runCount: schedule.runCount + 1,
        );
        _schedules[scheduleIndex] = updatedSchedule;
        await StorageService.saveSchedule(updatedSchedule);
        notifyListeners();
      }

      // تشغيل الجدولة عبر API
      // await _apiService.runScheduleNow(scheduleId);
      
      _clearError();
    } catch (e) {
      _setError('فشل في تشغيل الجدولة: $e');
    }
  }

  Schedule? getScheduleById(int id) {
    try {
      return _schedules.firstWhere((schedule) => schedule.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Schedule> getActiveSchedules() {
    return _schedules.where((schedule) => schedule.isActive).toList();
  }

  List<Schedule> getSchedulesByFrequency(ScheduleFrequency frequency) {
    return _schedules.where((schedule) => schedule.frequency == frequency).toList();
  }

  List<Schedule> getSchedulesByPost(int postId) {
    return _schedules.where((schedule) => schedule.postId == postId).toList();
  }

  List<Schedule> getSchedulesByAccount(int accountId) {
    return _schedules.where((schedule) => 
        schedule.accountIds.contains(accountId)).toList();
  }

  List<Schedule> getUpcomingSchedules() {
    final now = DateTime.now();
    return _schedules.where((schedule) => 
        schedule.isActive && 
        schedule.nextRun != null && 
        schedule.nextRun!.isAfter(now)).toList()
      ..sort((a, b) => a.nextRun!.compareTo(b.nextRun!));
  }

  List<Schedule> getOverdueSchedules() {
    final now = DateTime.now();
    return _schedules.where((schedule) => 
        schedule.isActive && 
        schedule.nextRun != null && 
        schedule.nextRun!.isBefore(now)).toList();
  }

  int get totalSchedules => _schedules.length;
  int get activeSchedulesCount => getActiveSchedules().length;
  int get inactiveSchedulesCount => _schedules.where((s) => !s.isActive).length;
  int get upcomingSchedulesCount => getUpcomingSchedules().length;
  int get overdueSchedulesCount => getOverdueSchedules().length;

  // إحصائيات حسب التكرار
  int get onceSchedulesCount => getSchedulesByFrequency(ScheduleFrequency.once).length;
  int get dailySchedulesCount => getSchedulesByFrequency(ScheduleFrequency.daily).length;
  int get weeklySchedulesCount => getSchedulesByFrequency(ScheduleFrequency.weekly).length;
  int get monthlySchedulesCount => getSchedulesByFrequency(ScheduleFrequency.monthly).length;

  // الحصول على الجدولة التالية
  Schedule? getNextSchedule() {
    final upcoming = getUpcomingSchedules();
    return upcoming.isNotEmpty ? upcoming.first : null;
  }

  // الحصول على إجمالي عدد مرات التشغيل
  int get totalRunCount {
    return _schedules.fold(0, (sum, schedule) => sum + schedule.runCount);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
