import 'package:hive_flutter/hive_flutter.dart';
import 'package:encrypt/encrypt.dart';
import '../models/models.dart';

class StorageService {
  static const String _accountsBox = 'accounts';
  static const String _postsBox = 'posts';
  static const String _proxiesBox = 'proxies';
  static const String _schedulesBox = 'schedules';
  static const String _logsBox = 'logs';
  static const String _settingsBox = 'settings';
  
  static late Box<Account> _accountsBoxInstance;
  static late Box<Post> _postsBoxInstance;
  static late Box<ProxyModel> _proxiesBoxInstance;
  static late Box<Schedule> _schedulesBoxInstance;
  static late Box<LogEntry> _logsBoxInstance;
  static late Box _settingsBoxInstance;
  
  static late Encrypter _encrypter;
  static late IV _iv;

  // Initialize Hive and open boxes
  static Future<void> init() async {
    await Hive.initFlutter();

    // Register adapters
    Hive.registerAdapter(AccountAdapter());
    Hive.registerAdapter(AccountStatusAdapter());
    Hive.registerAdapter(PostAdapter());
    Hive.registerAdapter(PostImageAdapter());
    Hive.registerAdapter(PostStatusAdapter());
    Hive.registerAdapter(ProxyModelAdapter());
    Hive.registerAdapter(ProxyTypeAdapter());
    Hive.registerAdapter(ProxyStatusAdapter());
    Hive.registerAdapter(ScheduleAdapter());
    Hive.registerAdapter(ScheduleFrequencyAdapter());
    Hive.registerAdapter(LogEntryAdapter());
    Hive.registerAdapter(LogLevelAdapter());
    Hive.registerAdapter(LogCategoryAdapter());

    // Open boxes first
    _accountsBoxInstance = await Hive.openBox<Account>(_accountsBox);
    _postsBoxInstance = await Hive.openBox<Post>(_postsBox);
    _proxiesBoxInstance = await Hive.openBox<ProxyModel>(_proxiesBox);
    _schedulesBoxInstance = await Hive.openBox<Schedule>(_schedulesBox);
    _logsBoxInstance = await Hive.openBox<LogEntry>(_logsBox);
    _settingsBoxInstance = await Hive.openBox(_settingsBox);

    // Initialize encryption after opening settings box
    _initEncryption();
  }

  // Initialize encryption for sensitive data
  static void _initEncryption() {
    // Generate or retrieve encryption key
    String? keyString = _settingsBoxInstance.get('encryption_key');
    if (keyString == null) {
      // Generate new key
      final key = Key.fromSecureRandom(32);
      keyString = key.base64;
      _settingsBoxInstance.put('encryption_key', keyString);
    }
    
    final key = Key.fromBase64(keyString);
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }

  // Encryption helpers
  static String encryptData(String data) {
    final encrypted = _encrypter.encrypt(data, iv: _iv);
    return '${_iv.base64}:${encrypted.base64}';
  }

  static String decryptData(String encryptedData) {
    final parts = encryptedData.split(':');
    if (parts.length != 2) throw Exception('Invalid encrypted data format');
    
    final iv = IV.fromBase64(parts[0]);
    final encrypted = Encrypted.fromBase64(parts[1]);
    return _encrypter.decrypt(encrypted, iv: iv);
  }

  // Account operations
  static Future<void> saveAccount(Account account) async {
    await _accountsBoxInstance.put(account.id, account);
  }

  static Account? getAccount(int id) {
    return _accountsBoxInstance.get(id);
  }

  static List<Account> getAllAccounts() {
    return _accountsBoxInstance.values.toList();
  }

  static Future<void> deleteAccount(int id) async {
    await _accountsBoxInstance.delete(id);
  }

  static Future<void> clearAccounts() async {
    await _accountsBoxInstance.clear();
  }

  // Post operations
  static Future<void> savePost(Post post) async {
    await _postsBoxInstance.put(post.id, post);
  }

  static Post? getPost(int id) {
    return _postsBoxInstance.get(id);
  }

  static List<Post> getAllPosts() {
    return _postsBoxInstance.values.toList();
  }

  static List<Post> getPostTemplates() {
    return _postsBoxInstance.values.where((post) => post.isTemplate).toList();
  }

  static Future<void> deletePost(int id) async {
    await _postsBoxInstance.delete(id);
  }

  static Future<void> clearPosts() async {
    await _postsBoxInstance.clear();
  }

  // Proxy operations
  static Future<void> saveProxy(ProxyModel proxy) async {
    await _proxiesBoxInstance.put(proxy.id, proxy);
  }

  static ProxyModel? getProxy(int id) {
    return _proxiesBoxInstance.get(id);
  }

  static List<ProxyModel> getAllProxies() {
    return _proxiesBoxInstance.values.toList();
  }

  static List<ProxyModel> getActiveProxies() {
    return _proxiesBoxInstance.values
        .where((proxy) => proxy.isActive && proxy.status == ProxyStatus.active)
        .toList();
  }

  static Future<void> deleteProxy(int id) async {
    await _proxiesBoxInstance.delete(id);
  }

  static Future<void> clearProxies() async {
    await _proxiesBoxInstance.clear();
  }

  // Schedule operations
  static Future<void> saveSchedule(Schedule schedule) async {
    await _schedulesBoxInstance.put(schedule.id, schedule);
  }

  static Schedule? getSchedule(int id) {
    return _schedulesBoxInstance.get(id);
  }

  static List<Schedule> getAllSchedules() {
    return _schedulesBoxInstance.values.toList();
  }

  static List<Schedule> getActiveSchedules() {
    return _schedulesBoxInstance.values
        .where((schedule) => schedule.isActive)
        .toList();
  }

  static Future<void> deleteSchedule(int id) async {
    await _schedulesBoxInstance.delete(id);
  }

  static Future<void> clearSchedules() async {
    await _schedulesBoxInstance.clear();
  }

  // Log operations
  static Future<void> saveLog(LogEntry log) async {
    await _logsBoxInstance.add(log);
    
    // Keep only last 1000 logs
    if (_logsBoxInstance.length > 1000) {
      final keysToDelete = _logsBoxInstance.keys.take(_logsBoxInstance.length - 1000);
      for (final key in keysToDelete) {
        await _logsBoxInstance.delete(key);
      }
    }
  }

  static List<LogEntry> getAllLogs() {
    return _logsBoxInstance.values.toList().reversed.toList();
  }

  static List<LogEntry> getLogsByLevel(LogLevel level) {
    return _logsBoxInstance.values
        .where((log) => log.level == level)
        .toList()
        .reversed
        .toList();
  }

  static List<LogEntry> getLogsByCategory(LogCategory category) {
    return _logsBoxInstance.values
        .where((log) => log.category == category)
        .toList()
        .reversed
        .toList();
  }

  static Future<void> clearLogs() async {
    await _logsBoxInstance.clear();
  }

  // Settings operations
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBoxInstance.put(key, value);
  }

  static T? getSetting<T>(String key, {T? defaultValue}) {
    return _settingsBoxInstance.get(key, defaultValue: defaultValue) as T?;
  }

  static Future<void> deleteSetting(String key) async {
    await _settingsBoxInstance.delete(key);
  }

  static Future<void> clearSettings() async {
    await _settingsBoxInstance.clear();
  }

  // Backup and restore
  static Map<String, dynamic> exportData() {
    return {
      'accounts': getAllAccounts().map((a) => a.toJson()).toList(),
      'posts': getAllPosts().map((p) => p.toJson()).toList(),
      'proxies': getAllProxies().map((p) => p.toJson()).toList(),
      'schedules': getAllSchedules().map((s) => s.toJson()).toList(),
      'settings': Map.fromEntries(_settingsBoxInstance.toMap().entries),
      'exportDate': DateTime.now().toIso8601String(),
    };
  }

  static Future<void> importData(Map<String, dynamic> data) async {
    // Clear existing data
    await clearAccounts();
    await clearPosts();
    await clearProxies();
    await clearSchedules();

    // Import accounts
    if (data['accounts'] != null) {
      for (final accountJson in data['accounts']) {
        final account = Account.fromJson(accountJson);
        await saveAccount(account);
      }
    }

    // Import posts
    if (data['posts'] != null) {
      for (final postJson in data['posts']) {
        final post = Post.fromJson(postJson);
        await savePost(post);
      }
    }

    // Import proxies
    if (data['proxies'] != null) {
      for (final proxyJson in data['proxies']) {
        final proxy = ProxyModel.fromJson(proxyJson);
        await saveProxy(proxy);
      }
    }

    // Import schedules
    if (data['schedules'] != null) {
      for (final scheduleJson in data['schedules']) {
        final schedule = Schedule.fromJson(scheduleJson);
        await saveSchedule(schedule);
      }
    }

    // Import settings
    if (data['settings'] != null) {
      for (final entry in data['settings'].entries) {
        await saveSetting(entry.key, entry.value);
      }
    }
  }

  // Close all boxes
  static Future<void> dispose() async {
    await _accountsBoxInstance.close();
    await _postsBoxInstance.close();
    await _proxiesBoxInstance.close();
    await _schedulesBoxInstance.close();
    await _logsBoxInstance.close();
    await _settingsBoxInstance.close();
  }
}
