import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

class WebSocketService {
  static const String wsUrl = 'ws://localhost:3000';
  
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _messageController;
  Timer? _reconnectTimer;
  Timer? _pingTimer;
  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const Duration pingInterval = Duration(seconds: 30);

  // Stream for incoming messages
  Stream<Map<String, dynamic>> get messageStream => 
      _messageController?.stream ?? const Stream.empty();

  // Connection status
  bool get isConnected => _isConnected;

  // Initialize WebSocket connection
  Future<void> connect() async {
    if (_isConnected) return;

    try {
      _messageController ??= StreamController<Map<String, dynamic>>.broadcast();
      
      debugPrint('Connecting to WebSocket: $wsUrl');
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      debugPrint('WebSocket connected successfully');

      // Start ping timer to keep connection alive
      _startPingTimer();

    } catch (e) {
      debugPrint('WebSocket connection failed: $e');
      _onError(e);
    }
  }

  // Disconnect WebSocket
  void disconnect() {
    _shouldReconnect = false;
    _isConnected = false;
    
    _pingTimer?.cancel();
    _reconnectTimer?.cancel();
    
    _channel?.sink.close(status.normalClosure);
    _channel = null;
    
    debugPrint('WebSocket disconnected');
  }

  // Send message to server
  void sendMessage(Map<String, dynamic> message) {
    if (_isConnected && _channel != null) {
      try {
        final jsonMessage = jsonEncode(message);
        _channel!.sink.add(jsonMessage);
        debugPrint('Sent WebSocket message: $jsonMessage');
      } catch (e) {
        debugPrint('Error sending WebSocket message: $e');
      }
    } else {
      debugPrint('Cannot send message: WebSocket not connected');
    }
  }

  // Handle incoming messages
  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> message = jsonDecode(data);
      debugPrint('Received WebSocket message: $message');
      
      // Handle ping/pong
      if (message['type'] == 'pong') {
        debugPrint('Received pong from server');
        return;
      }

      // Broadcast message to listeners
      _messageController?.add(message);
    } catch (e) {
      debugPrint('Error parsing WebSocket message: $e');
    }
  }

  // Handle connection errors
  void _onError(dynamic error) {
    debugPrint('WebSocket error: $error');
    _isConnected = false;
    
    if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  // Handle disconnection
  void _onDisconnected() {
    debugPrint('WebSocket disconnected');
    _isConnected = false;
    _pingTimer?.cancel();
    
    if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  // Schedule reconnection attempt
  void _scheduleReconnect() {
    _reconnectAttempts++;
    debugPrint('Scheduling reconnect attempt $_reconnectAttempts/$maxReconnectAttempts');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  // Start ping timer to keep connection alive
  void _startPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(pingInterval, (timer) {
      if (_isConnected) {
        sendMessage({'type': 'ping'});
      } else {
        timer.cancel();
      }
    });
  }

  // Dispose resources
  void dispose() {
    disconnect();
    _messageController?.close();
    _messageController = null;
  }

  // Convenience methods for specific message types
  void subscribeToAccountUpdates(int accountId) {
    sendMessage({
      'type': 'subscribe',
      'channel': 'account_updates',
      'accountId': accountId,
    });
  }

  void subscribeToPostUpdates(int postId) {
    sendMessage({
      'type': 'subscribe',
      'channel': 'post_updates',
      'postId': postId,
    });
  }

  void subscribeToScheduleUpdates() {
    sendMessage({
      'type': 'subscribe',
      'channel': 'schedule_updates',
    });
  }

  void subscribeToLogs() {
    sendMessage({
      'type': 'subscribe',
      'channel': 'logs',
    });
  }

  void unsubscribeFromChannel(String channel) {
    sendMessage({
      'type': 'unsubscribe',
      'channel': channel,
    });
  }
}
