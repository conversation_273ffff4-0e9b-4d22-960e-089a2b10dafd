import 'package:hive/hive.dart';

part 'schedule.g.dart';

@HiveType(typeId: 8)
class Schedule extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String name;

  @HiveField(2)
  int postId;

  @HiveField(3)
  List<int> accountIds;

  @HiveField(4)
  DateTime startDate;

  @HiveField(5)
  DateTime? endDate;

  @HiveField(6)
  ScheduleFrequency frequency;

  @HiveField(7)
  int interval;

  @HiveField(8)
  int randomDelay; // in minutes

  @HiveField(9)
  bool isActive;

  @HiveField(10)
  DateTime? nextRun;

  @HiveField(11)
  DateTime? lastRun;

  @HiveField(12)
  int runCount;

  @HiveField(13)
  DateTime createdAt;

  @HiveField(14)
  Map<String, dynamic>? settings;

  Schedule({
    this.id,
    required this.name,
    required this.postId,
    List<int>? accountIds,
    required this.startDate,
    this.endDate,
    this.frequency = ScheduleFrequency.once,
    this.interval = 1,
    this.randomDelay = 0,
    this.isActive = true,
    this.nextRun,
    this.lastRun,
    this.runCount = 0,
    DateTime? createdAt,
    this.settings,
  }) : 
    accountIds = accountIds ?? [],
    createdAt = createdAt ?? DateTime.now();

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'postId': postId,
      'accountIds': accountIds,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'frequency': frequency.name,
      'interval': interval,
      'randomDelay': randomDelay,
      'isActive': isActive,
      'nextRun': nextRun?.toIso8601String(),
      'lastRun': lastRun?.toIso8601String(),
      'runCount': runCount,
      'createdAt': createdAt.toIso8601String(),
      'settings': settings,
    };
  }

  // Create from JSON response
  factory Schedule.fromJson(Map<String, dynamic> json) {
    return Schedule(
      id: json['id'],
      name: json['name'],
      postId: json['postId'],
      accountIds: List<int>.from(json['accountIds'] ?? []),
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null 
          ? DateTime.parse(json['endDate']) 
          : null,
      frequency: ScheduleFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
        orElse: () => ScheduleFrequency.once,
      ),
      interval: json['interval'] ?? 1,
      randomDelay: json['randomDelay'] ?? 0,
      isActive: json['isActive'] ?? true,
      nextRun: json['nextRun'] != null 
          ? DateTime.parse(json['nextRun']) 
          : null,
      lastRun: json['lastRun'] != null 
          ? DateTime.parse(json['lastRun']) 
          : null,
      runCount: json['runCount'] ?? 0,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      settings: json['settings'],
    );
  }

  // Copy with method for updates
  Schedule copyWith({
    int? id,
    String? name,
    int? postId,
    List<int>? accountIds,
    DateTime? startDate,
    DateTime? endDate,
    ScheduleFrequency? frequency,
    int? interval,
    int? randomDelay,
    bool? isActive,
    DateTime? nextRun,
    DateTime? lastRun,
    int? runCount,
    DateTime? createdAt,
    Map<String, dynamic>? settings,
  }) {
    return Schedule(
      id: id ?? this.id,
      name: name ?? this.name,
      postId: postId ?? this.postId,
      accountIds: accountIds ?? this.accountIds,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      frequency: frequency ?? this.frequency,
      interval: interval ?? this.interval,
      randomDelay: randomDelay ?? this.randomDelay,
      isActive: isActive ?? this.isActive,
      nextRun: nextRun ?? this.nextRun,
      lastRun: lastRun ?? this.lastRun,
      runCount: runCount ?? this.runCount,
      createdAt: createdAt ?? this.createdAt,
      settings: settings ?? this.settings,
    );
  }

  @override
  String toString() {
    return 'Schedule(id: $id, name: $name, frequency: $frequency, isActive: $isActive)';
  }
}

@HiveType(typeId: 9)
enum ScheduleFrequency {
  @HiveField(0)
  once,

  @HiveField(1)
  daily,

  @HiveField(2)
  weekly,

  @HiveField(3)
  monthly,

  @HiveField(4)
  custom,
}

extension ScheduleFrequencyExtension on ScheduleFrequency {
  String get displayName {
    switch (this) {
      case ScheduleFrequency.once:
        return 'مرة واحدة';
      case ScheduleFrequency.daily:
        return 'يومياً';
      case ScheduleFrequency.weekly:
        return 'أسبوعياً';
      case ScheduleFrequency.monthly:
        return 'شهرياً';
      case ScheduleFrequency.custom:
        return 'مخصص';
    }
  }

  String get displayNameEn {
    switch (this) {
      case ScheduleFrequency.once:
        return 'Once';
      case ScheduleFrequency.daily:
        return 'Daily';
      case ScheduleFrequency.weekly:
        return 'Weekly';
      case ScheduleFrequency.monthly:
        return 'Monthly';
      case ScheduleFrequency.custom:
        return 'Custom';
    }
  }
}
