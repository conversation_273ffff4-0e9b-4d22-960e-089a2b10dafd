const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// إنشاء صورة اختبار بسيطة
function createTestImage() {
    const testImagePath = path.join(__dirname, 'test-image.png');
    
    // إنشاء صورة PNG بسيطة (1x1 pixel)
    const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, 0x01, // width: 1
        0x00, 0x00, 0x00, 0x01, // height: 1
        0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
        0x90, 0x77, 0x53, 0xDE, // CRC
        0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,
        0xE2, 0x21, 0xBC, 0x33, // CRC
        0x00, 0x00, 0x00, 0x00, // IEND chunk length
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
    ]);
    
    fs.writeFileSync(testImagePath, pngData);
    return testImagePath;
}

async function testImageSystem() {
    const baseUrl = 'http://localhost:3000';
    
    console.log('🧪 Testing Image System...\n');
    
    try {
        // 1. إنشاء صورة اختبار
        console.log('1. Creating test image...');
        const testImagePath = createTestImage();
        console.log(`✅ Test image created: ${testImagePath}\n`);
        
        // 2. إنشاء منشور جديد مع صورة
        console.log('2. Creating new post with image...');
        
        const formData = new FormData();
        formData.append('title', 'منشور اختبار الصور');
        formData.append('description', 'هذا منشور لاختبار نظام رفع الصور');
        formData.append('price', '100');
        formData.append('category', 'إلكترونيات');
        formData.append('location', 'القاهرة');
        formData.append('accountIds', JSON.stringify([]));
        
        // إضافة الصورة
        formData.append('images', fs.createReadStream(testImagePath), {
            filename: 'test-image.png',
            contentType: 'image/png'
        });
        
        const createResponse = await axios.post(`${baseUrl}/api/posts`, formData, {
            headers: {
                ...formData.getHeaders(),
            },
        });
        
        console.log(`✅ Post created successfully with ID: ${createResponse.data.id}`);
        const postId = createResponse.data.id;
        
        // 3. التحقق من رفع الصورة
        console.log('\n3. Checking uploaded image...');
        const postResponse = await axios.get(`${baseUrl}/api/posts/${postId}`);
        const post = postResponse.data;
        
        if (post.images && post.images.length > 0) {
            console.log(`✅ Image uploaded successfully: ${post.images[0].filename}`);
            console.log(`   Original name: ${post.images[0].originalName}`);
            console.log(`   Size: ${post.images[0].size} bytes`);
            
            // 4. اختبار الوصول للصورة
            console.log('\n4. Testing image access...');
            const imageUrl = `${baseUrl}/uploads/${post.images[0].filename}`;
            
            try {
                const imageResponse = await axios.get(imageUrl, {
                    responseType: 'arraybuffer'
                });
                console.log(`✅ Image accessible at: ${imageUrl}`);
                console.log(`   Response size: ${imageResponse.data.length} bytes`);
                console.log(`   Content-Type: ${imageResponse.headers['content-type']}`);
            } catch (imageError) {
                console.log(`❌ Image not accessible: ${imageError.message}`);
            }
            
            // 5. اختبار API للصور
            console.log('\n5. Testing images API...');
            try {
                const imagesResponse = await axios.get(`${baseUrl}/api/posts/${postId}/images`);
                const images = imagesResponse.data;
                
                if (images && images.length > 0) {
                    console.log(`✅ Images API working: ${images.length} images found`);
                    console.log(`   Available URLs:`);
                    const image = images[0];
                    if (image.urls) {
                        Object.entries(image.urls).forEach(([size, url]) => {
                            console.log(`     ${size}: ${url}`);
                        });
                    }
                } else {
                    console.log(`⚠️ Images API returned empty array`);
                }
            } catch (apiError) {
                console.log(`❌ Images API error: ${apiError.message}`);
            }
            
        } else {
            console.log(`❌ No images found in post`);
        }
        
        // 6. تنظيف - حذف الملفات المؤقتة
        console.log('\n6. Cleaning up...');
        try {
            fs.unlinkSync(testImagePath);
            console.log(`✅ Test image deleted`);
        } catch (cleanupError) {
            console.log(`⚠️ Cleanup warning: ${cleanupError.message}`);
        }
        
        console.log('\n🎉 Image system test completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testImageSystem().catch(console.error);
}

module.exports = { testImageSystem };
