const Database = require('better-sqlite3');
const path = require('path');

// إنشاء قاعدة البيانات
const dbPath = path.join(__dirname, 'marketplace.db');
const db = new Database(dbPath);

// تفعيل WAL mode للأداء الأفضل
db.pragma('journal_mode = WAL');

// إنشاء الجداول
function initializeDatabase() {
  console.log('Initializing database...');
  // جدول الحسابات
  db.exec(`
    CREATE TABLE IF NOT EXISTS accounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL UNIQUE,
      password TEXT NOT NULL,
      status TEXT DEFAULT 'active',
      facebook_status TEXT DEFAULT 'unknown',
      last_tested DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول المنشورات
  db.exec(`
    CREATE TABLE IF NOT EXISTS posts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      price REAL NOT NULL,
      category TEXT NOT NULL,
      location TEXT,
      status TEXT DEFAULT 'draft',
      scheduled_at DATETIME,
      published_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول صور المنشورات
  db.exec(`
    CREATE TABLE IF NOT EXISTS post_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      post_id INTEGER NOT NULL,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      path TEXT NOT NULL,
      size INTEGER DEFAULT 0,
      is_url BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE
    )
  `);

  // جدول ربط المنشورات بالحسابات
  db.exec(`
    CREATE TABLE IF NOT EXISTS post_accounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      post_id INTEGER NOT NULL,
      account_id INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE,
      FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
      UNIQUE(post_id, account_id)
    )
  `);

  // جدول البروكسيات
  db.exec(`
    CREATE TABLE IF NOT EXISTS proxies (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      host TEXT NOT NULL,
      port INTEGER NOT NULL,
      username TEXT,
      password TEXT,
      type TEXT DEFAULT 'http',
      status TEXT DEFAULT 'untested',
      current_ip TEXT,
      response_time TEXT,
      last_tested DATETIME,
      last_error TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول الجدولة
  db.exec(`
    CREATE TABLE IF NOT EXISTS schedules (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      post_id INTEGER NOT NULL,
      frequency TEXT NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      last_run DATETIME,
      next_run DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE
    )
  `);

  // جدول ربط الجدولة بالحسابات
  db.exec(`
    CREATE TABLE IF NOT EXISTS schedule_accounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      schedule_id INTEGER NOT NULL,
      account_id INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (schedule_id) REFERENCES schedules (id) ON DELETE CASCADE,
      FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
      UNIQUE(schedule_id, account_id)
    )
  `);

  // جدول السجلات
  db.exec(`
    CREATE TABLE IF NOT EXISTS logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      level TEXT NOT NULL,
      message TEXT NOT NULL,
      category TEXT,
      details TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // إنشاء الفهارس للأداء الأفضل
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
    CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at);
    CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
    CREATE INDEX IF NOT EXISTS idx_logs_category ON logs(category);
    CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at);
  `);

  console.log('Database initialized successfully');
}

// تهيئة قاعدة البيانات فوراً
initializeDatabase();

// دوال مساعدة للحسابات
const accountQueries = {
  getAll: db.prepare('SELECT * FROM accounts ORDER BY created_at DESC'),
  getById: db.prepare('SELECT * FROM accounts WHERE id = ?'),
  create: db.prepare(`
    INSERT INTO accounts (name, email, password, status, facebook_status)
    VALUES (?, ?, ?, ?, ?)
  `),
  update: db.prepare(`
    UPDATE accounts 
    SET name = ?, email = ?, password = ?, status = ?, facebook_status = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  delete: db.prepare('DELETE FROM accounts WHERE id = ?'),
  updateStatus: db.prepare(`
    UPDATE accounts 
    SET status = ?, facebook_status = ?, last_tested = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `)
};

// دوال مساعدة للمنشورات
const postQueries = {
  getAll: db.prepare('SELECT * FROM posts ORDER BY created_at DESC'),
  getById: db.prepare('SELECT * FROM posts WHERE id = ?'),
  create: db.prepare(`
    INSERT INTO posts (title, description, price, category, location, status)
    VALUES (?, ?, ?, ?, ?, ?)
  `),
  update: db.prepare(`
    UPDATE posts 
    SET title = ?, description = ?, price = ?, category = ?, location = ?, status = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  delete: db.prepare('DELETE FROM posts WHERE id = ?'),
  updateStatus: db.prepare(`
    UPDATE posts 
    SET status = ?, published_at = CASE WHEN ? = 'published' THEN CURRENT_TIMESTAMP ELSE published_at END, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  schedule: db.prepare(`
    UPDATE posts 
    SET scheduled_at = ?, status = 'scheduled', updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `)
};

// دوال مساعدة لصور المنشورات
const postImageQueries = {
  getByPostId: db.prepare('SELECT * FROM post_images WHERE post_id = ? ORDER BY id'),
  getById: db.prepare('SELECT * FROM post_images WHERE id = ?'),
  create: db.prepare(`
    INSERT INTO post_images (post_id, filename, original_name, path, size, is_url)
    VALUES (?, ?, ?, ?, ?, ?)
  `),
  deleteByPostId: db.prepare('DELETE FROM post_images WHERE post_id = ?'),
  deleteById: db.prepare('DELETE FROM post_images WHERE id = ?'),
  // updateOrder: db.prepare('UPDATE post_images SET display_order = ? WHERE id = ?') // Disabled until column is added
};

// دوال مساعدة لربط المنشورات بالحسابات
const postAccountQueries = {
  getByPostId: db.prepare('SELECT account_id FROM post_accounts WHERE post_id = ?'),
  create: db.prepare('INSERT OR IGNORE INTO post_accounts (post_id, account_id) VALUES (?, ?)'),
  deleteByPostId: db.prepare('DELETE FROM post_accounts WHERE post_id = ?')
};

// دوال مساعدة للبروكسيات
const proxyQueries = {
  getAll: db.prepare('SELECT * FROM proxies ORDER BY created_at DESC'),
  getById: db.prepare('SELECT * FROM proxies WHERE id = ?'),
  create: db.prepare(`
    INSERT INTO proxies (name, host, port, username, password, type, status)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `),
  update: db.prepare(`
    UPDATE proxies 
    SET name = ?, host = ?, port = ?, username = ?, password = ?, type = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  delete: db.prepare('DELETE FROM proxies WHERE id = ?'),
  updateStatus: db.prepare(`
    UPDATE proxies 
    SET status = ?, current_ip = ?, response_time = ?, last_tested = CURRENT_TIMESTAMP, last_error = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `)
};

// دوال مساعدة للجدولة
const scheduleQueries = {
  getAll: db.prepare('SELECT * FROM schedules ORDER BY created_at DESC'),
  getById: db.prepare('SELECT * FROM schedules WHERE id = ?'),
  create: db.prepare(`
    INSERT INTO schedules (name, post_id, frequency, is_active, next_run)
    VALUES (?, ?, ?, ?, ?)
  `),
  update: db.prepare(`
    UPDATE schedules 
    SET name = ?, post_id = ?, frequency = ?, is_active = ?, next_run = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
  delete: db.prepare('DELETE FROM schedules WHERE id = ?'),
  updateRun: db.prepare(`
    UPDATE schedules 
    SET last_run = CURRENT_TIMESTAMP, next_run = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `)
};

// دوال مساعدة لربط الجدولة بالحسابات
const scheduleAccountQueries = {
  getByScheduleId: db.prepare('SELECT account_id FROM schedule_accounts WHERE schedule_id = ?'),
  create: db.prepare('INSERT OR IGNORE INTO schedule_accounts (schedule_id, account_id) VALUES (?, ?)'),
  deleteByScheduleId: db.prepare('DELETE FROM schedule_accounts WHERE schedule_id = ?')
};

// دوال مساعدة للسجلات
const logQueries = {
  getAll: db.prepare(`
    SELECT * FROM logs 
    WHERE (?1 IS NULL OR level = ?1) 
    AND (?2 IS NULL OR category = ?2)
    ORDER BY created_at DESC 
    LIMIT ? OFFSET ?
  `),
  getCount: db.prepare(`
    SELECT COUNT(*) as count FROM logs 
    WHERE (?1 IS NULL OR level = ?1) 
    AND (?2 IS NULL OR category = ?2)
  `),
  create: db.prepare(`
    INSERT INTO logs (level, message, category, details)
    VALUES (?, ?, ?, ?)
  `),
  deleteAll: db.prepare('DELETE FROM logs')
};

module.exports = {
  db,
  initializeDatabase,
  accountQueries,
  postQueries,
  postImageQueries,
  postAccountQueries,
  proxyQueries,
  scheduleQueries,
  scheduleAccountQueries,
  logQueries
};
