const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'));
        }
    }
});

// In-memory storage for demo
let posts = [];
let postIdCounter = 1;

// GET /api/posts - Get all posts
router.get('/', (req, res) => {
    try {
        res.json(posts);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/posts - Create new post
router.post('/', upload.array('images', 10), (req, res) => {
    try {
        const { title, description, price, category, location, accountIds } = req.body;
        
        if (!title || !description || !price || !category) {
            return res.status(400).json({ error: 'Title, description, price, and category are required' });
        }
        
        // Process uploaded images
        const images = req.files ? req.files.map(file => ({
            filename: file.filename,
            originalName: file.originalname,
            path: file.path,
            size: file.size
        })) : [];
        
        const newPost = {
            id: postIdCounter++,
            title,
            description,
            price: parseFloat(price),
            category,
            location: location || '',
            images,
            accountIds: accountIds ? JSON.parse(accountIds) : [],
            status: 'draft',
            createdAt: new Date().toISOString(),
            scheduledAt: null,
            publishedAt: null
        };
        
        posts.push(newPost);
        res.status(201).json(newPost);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// PUT /api/posts/:id - Update post
router.put('/:id', upload.array('images', 10), (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const postIndex = posts.findIndex(post => post.id === postId);
        
        if (postIndex === -1) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        const post = posts[postIndex];
        const { title, description, price, category, location, accountIds } = req.body;
        
        // Update fields
        if (title) post.title = title;
        if (description) post.description = description;
        if (price) post.price = parseFloat(price);
        if (category) post.category = category;
        if (location !== undefined) post.location = location;
        if (accountIds) post.accountIds = JSON.parse(accountIds);
        
        // Handle new images
        if (req.files && req.files.length > 0) {
            const newImages = req.files.map(file => ({
                filename: file.filename,
                originalName: file.originalname,
                path: file.path,
                size: file.size
            }));
            post.images = [...post.images, ...newImages];
        }
        
        res.json(post);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// DELETE /api/posts/:id - Delete post
router.delete('/:id', (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const postIndex = posts.findIndex(post => post.id === postId);
        
        if (postIndex === -1) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        posts.splice(postIndex, 1);
        res.json({ message: 'Post deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/posts/:id/publish - Publish post to selected accounts
router.post('/:id/publish', async (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const post = posts.find(p => p.id === postId);
        
        if (!post) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        if (post.accountIds.length === 0) {
            return res.status(400).json({ error: 'No accounts selected for publishing' });
        }
        
        // TODO: Implement actual publishing with Playwright
        // For now, simulate publishing
        post.status = 'publishing';
        post.publishedAt = new Date().toISOString();
        
        setTimeout(() => {
            post.status = 'published';
            res.json({ success: true, message: 'Post published successfully' });
        }, 3000);
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/posts/:id/schedule - Schedule post
router.post('/:id/schedule', (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const { scheduledAt } = req.body;
        
        const post = posts.find(p => p.id === postId);
        
        if (!post) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        if (!scheduledAt) {
            return res.status(400).json({ error: 'Scheduled time is required' });
        }
        
        post.scheduledAt = scheduledAt;
        post.status = 'scheduled';
        
        res.json(post);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// GET /api/posts/templates - Get post templates
router.get('/templates', (req, res) => {
    try {
        const templates = posts.filter(post => post.isTemplate);
        res.json(templates);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/posts/:id/save-template - Save post as template
router.post('/:id/save-template', (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const post = posts.find(p => p.id === postId);
        
        if (!post) {
            return res.status(404).json({ error: 'Post not found' });
        }
        
        post.isTemplate = true;
        res.json(post);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
