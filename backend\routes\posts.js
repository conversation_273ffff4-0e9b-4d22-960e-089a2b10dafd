const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const {
  postQueries,
  postImageQueries,
  postAccountQueries,
  logQueries
} = require('../database');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '..', 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'));
        }
    }
});

// Helper function to get post with images and accounts
function getPostWithDetails(postId) {
    const post = postQueries.getById.get(postId);
    if (!post) return null;

    const images = postImageQueries.getByPostId.all(postId);
    const accountIds = postAccountQueries.getByPostId.all(postId).map(row => row.account_id);

    return {
        ...post,
        images,
        accountIds
    };
}

// GET /api/posts - Get all posts
router.get('/', (req, res) => {
    try {
        const posts = postQueries.getAll.all();
        const postsWithDetails = posts.map(post => {
            const images = postImageQueries.getByPostId.all(post.id);
            const accountIds = postAccountQueries.getByPostId.all(post.id).map(row => row.account_id);
            return {
                ...post,
                images,
                accountIds
            };
        });
        res.json(postsWithDetails);
    } catch (error) {
        console.error('Error fetching posts:', error);
        logQueries.create.run('error', 'Failed to fetch posts', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to fetch posts' });
    }
});

// POST /api/posts - Create new post
router.post('/', upload.array('images', 10), (req, res) => {
    try {
        const { title, description, price, category, location, accountIds } = req.body;

        if (!title || !description || !price || !category) {
            return res.status(400).json({ error: 'Title, description, price, and category are required' });
        }

        // Create post in database
        const result = postQueries.create.run(
            title,
            description,
            parseFloat(price),
            category,
            location || '',
            'draft'
        );

        const postId = result.lastInsertRowid;

        // Process uploaded images
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                postImageQueries.create.run(
                    postId,
                    file.filename,
                    file.originalname,
                    file.path,
                    file.size,
                    false
                );
            }
        }

        // Link accounts to post
        if (accountIds) {
            const accountIdArray = JSON.parse(accountIds);
            for (const accountId of accountIdArray) {
                postAccountQueries.create.run(postId, accountId);
            }
        }

        // Get the complete post with details
        const newPost = getPostWithDetails(postId);

        logQueries.create.run('info', `Post created: ${title}`, 'posts', JSON.stringify({ id: postId }));
        res.status(201).json(newPost);
    } catch (error) {
        console.error('Error creating post:', error);
        logQueries.create.run('error', 'Failed to create post', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to create post' });
    }
});

// PUT /api/posts/:id - Update post
router.put('/:id', upload.array('images', 10), (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const existingPost = postQueries.getById.get(postId);

        if (!existingPost) {
            return res.status(404).json({ error: 'Post not found' });
        }

        const { title, description, price, category, location, accountIds, status, scheduledAt } = req.body;

        // Update post in database
        postQueries.update.run(
            title || existingPost.title,
            description || existingPost.description,
            price ? parseFloat(price) : existingPost.price,
            category || existingPost.category,
            location !== undefined ? location : existingPost.location,
            status || existingPost.status,
            postId
        );

        // Update scheduled time if provided
        if (scheduledAt !== undefined) {
            if (scheduledAt) {
                postQueries.schedule.run(scheduledAt, postId);
            }
        }

        // Update account associations
        if (accountIds) {
            postAccountQueries.deleteByPostId.run(postId);
            const accountIdArray = JSON.parse(accountIds);
            for (const accountId of accountIdArray) {
                postAccountQueries.create.run(postId, accountId);
            }
        }

        // Handle new images
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                postImageQueries.create.run(
                    postId,
                    file.filename,
                    file.originalname,
                    file.path,
                    file.size,
                    false
                );
            }
        }

        // Get updated post with details
        const updatedPost = getPostWithDetails(postId);

        logQueries.create.run('info', `Post updated: ${updatedPost.title}`, 'posts', JSON.stringify({ id: postId }));
        res.json(updatedPost);
    } catch (error) {
        console.error('Error updating post:', error);
        logQueries.create.run('error', 'Failed to update post', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to update post' });
    }
});

// DELETE /api/posts/:id - Delete post
router.delete('/:id', (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const existingPost = postQueries.getById.get(postId);

        if (!existingPost) {
            return res.status(404).json({ error: 'Post not found' });
        }

        // حماية القوالب من الحذف
        if (existingPost.is_template) {
            return res.status(400).json({
                error: 'Cannot delete template posts. Please remove template status first.'
            });
        }

        // Delete associated images from filesystem
        const images = postImageQueries.getByPostId.all(postId);
        for (const image of images) {
            try {
                if (fs.existsSync(image.path)) {
                    fs.unlinkSync(image.path);
                }
            } catch (err) {
                console.warn('Failed to delete image file:', image.path, err);
            }
        }

        // Delete from database (cascading will handle related records)
        postQueries.delete.run(postId);

        logQueries.create.run('info', `Post deleted: ${existingPost.title}`, 'posts', JSON.stringify({ id: postId }));
        res.json({ message: 'Post deleted successfully' });
    } catch (error) {
        console.error('Error deleting post:', error);
        logQueries.create.run('error', 'Failed to delete post', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to delete post' });
    }
});

// POST /api/posts/:id/publish - Publish post to selected accounts
router.post('/:id/publish', async (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const post = getPostWithDetails(postId);

        if (!post) {
            return res.status(404).json({ error: 'Post not found' });
        }

        if (post.accountIds.length === 0) {
            return res.status(400).json({ error: 'No accounts selected for publishing' });
        }

        // Update status to publishing
        postQueries.updateStatus.run('publishing', 'publishing', postId);

        // TODO: Implement actual publishing with Playwright
        // For now, simulate publishing
        setTimeout(() => {
            postQueries.updateStatus.run('published', 'published', postId);
            logQueries.create.run('info', `Post published: ${post.title}`, 'posts', JSON.stringify({ id: postId }));
        }, 3000);

        res.json({ success: true, message: 'Post publishing started' });

    } catch (error) {
        console.error('Error publishing post:', error);
        logQueries.create.run('error', 'Failed to publish post', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to publish post' });
    }
});

// POST /api/posts/:id/schedule - Schedule post
router.post('/:id/schedule', (req, res) => {
    try {
        const postId = parseInt(req.params.id);
        const { scheduledAt } = req.body;

        const existingPost = postQueries.getById.get(postId);

        if (!existingPost) {
            return res.status(404).json({ error: 'Post not found' });
        }

        if (!scheduledAt) {
            return res.status(400).json({ error: 'Scheduled time is required' });
        }

        // Update post with scheduled time
        postQueries.schedule.run(scheduledAt, postId);

        const updatedPost = getPostWithDetails(postId);

        logQueries.create.run('info', `Post scheduled: ${updatedPost.title}`, 'posts', JSON.stringify({ id: postId, scheduledAt }));
        res.json(updatedPost);
    } catch (error) {
        console.error('Error scheduling post:', error);
        logQueries.create.run('error', 'Failed to schedule post', 'posts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to schedule post' });
    }
});

module.exports = router;
