import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';
import 'package:system_tray/system_tray.dart';
import 'package:flutter/foundation.dart';

import 'services/storage_service.dart';
import 'services/api_service.dart';
import 'services/websocket_service.dart';
import 'providers/app_provider.dart';
import 'providers/account_provider.dart';
import 'providers/post_provider.dart';
import 'providers/proxy_provider.dart' as proxy_provider;
import 'providers/schedule_provider.dart';
import 'providers/log_provider.dart';
import 'screens/main_screen.dart';
import 'utils/app_theme.dart';
import 'utils/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize window manager for desktop
  if (!kIsWeb) {
    await windowManager.ensureInitialized();

    WindowOptions windowOptions = const WindowOptions(
      size: Size(1200, 800),
      minimumSize: Size(800, 600),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.normal,
      title: 'Facebook Marketplace Automation',
    );

    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }

  // Initialize storage
  await StorageService.init();

  runApp(const MarketplaceApp());
}

class MarketplaceApp extends StatefulWidget {
  const MarketplaceApp({super.key});

  @override
  State<MarketplaceApp> createState() => _MarketplaceAppState();
}

class _MarketplaceAppState extends State<MarketplaceApp> with WindowListener {
  final SystemTray _systemTray = SystemTray();
  final ApiService _apiService = ApiService();
  final WebSocketService _webSocketService = WebSocketService();

  @override
  void initState() {
    super.initState();
    windowManager.addListener(this);
    _initSystemTray();
    _connectToBackend();
  }

  @override
  void dispose() {
    windowManager.removeListener(this);
    _webSocketService.dispose();
    super.dispose();
  }

  Future<void> _initSystemTray() async {
    if (!kIsWeb) {
      try {
        await _systemTray.initSystemTray(
          title: "Marketplace App",
          iconPath: "assets/icons/app_icon.ico",
        );

        final Menu menu = Menu();
        await menu.buildFrom([
          MenuItemLabel(label: 'Show', onClicked: (menuItem) => _showWindow()),
          MenuItemLabel(label: 'Hide', onClicked: (menuItem) => _hideWindow()),
          MenuItemLabel(label: 'Exit', onClicked: (menuItem) => _exitApp()),
        ]);

        await _systemTray.setContextMenu(menu);
      } catch (e) {
        debugPrint('Failed to initialize system tray: $e');
      }
    }
  }

  Future<void> _connectToBackend() async {
    try {
      await _webSocketService.connect();
    } catch (e) {
      debugPrint('Failed to connect to backend: $e');
    }
  }

  void _showWindow() {
    windowManager.show();
    windowManager.focus();
  }

  void _hideWindow() {
    windowManager.hide();
  }

  void _exitApp() {
    windowManager.close();
  }

  @override
  void onWindowClose() async {
    bool isPreventClose = await windowManager.isPreventClose();
    if (isPreventClose) {
      _hideWindow();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => AccountProvider(_apiService)),
        ChangeNotifierProvider(create: (_) => PostProvider(_apiService)),
        ChangeNotifierProvider(create: (_) => proxy_provider.ProxyProvider(_apiService)),
        ChangeNotifierProvider(create: (_) => ScheduleProvider(_apiService)),
        ChangeNotifierProvider(create: (_) => LogProvider(_apiService)),
      ],
      child: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return MaterialApp(
            title: 'Facebook Marketplace Automation',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: appProvider.themeMode,
            locale: appProvider.locale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'),
              Locale('ar', 'SA'),
            ],
            home: const MainScreen(),
          );
        },
      ),
    );
  }
}
