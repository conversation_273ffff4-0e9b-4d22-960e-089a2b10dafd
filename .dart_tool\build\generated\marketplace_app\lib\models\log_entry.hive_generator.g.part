// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LogEntryAdapter extends TypeAdapter<LogEntry> {
  @override
  final int typeId = 10;

  @override
  LogEntry read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LogEntry(
      id: fields[0] as int?,
      timestamp: fields[1] as DateTime?,
      level: fields[2] as Log<PERSON>evel,
      category: fields[3] as Log<PERSON>ategory,
      message: fields[4] as String,
      details: (fields[5] as Map?)?.cast<String, dynamic>(),
      accountId: fields[6] as int?,
      postId: fields[7] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, LogEntry obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.timestamp)
      ..writeByte(2)
      ..write(obj.level)
      ..writeByte(3)
      ..write(obj.category)
      ..writeByte(4)
      ..write(obj.message)
      ..writeByte(5)
      ..write(obj.details)
      ..writeByte(6)
      ..write(obj.accountId)
      ..writeByte(7)
      ..write(obj.postId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LogEntryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LogLevelAdapter extends TypeAdapter<LogLevel> {
  @override
  final int typeId = 11;

  @override
  LogLevel read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LogLevel.debug;
      case 1:
        return LogLevel.info;
      case 2:
        return LogLevel.warn;
      case 3:
        return LogLevel.error;
      default:
        return LogLevel.debug;
    }
  }

  @override
  void write(BinaryWriter writer, LogLevel obj) {
    switch (obj) {
      case LogLevel.debug:
        writer.writeByte(0);
        break;
      case LogLevel.info:
        writer.writeByte(1);
        break;
      case LogLevel.warn:
        writer.writeByte(2);
        break;
      case LogLevel.error:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LogLevelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LogCategoryAdapter extends TypeAdapter<LogCategory> {
  @override
  final int typeId = 12;

  @override
  LogCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LogCategory.account;
      case 1:
        return LogCategory.post;
      case 2:
        return LogCategory.proxy;
      case 3:
        return LogCategory.scheduler;
      case 4:
        return LogCategory.system;
      case 5:
        return LogCategory.automation;
      default:
        return LogCategory.account;
    }
  }

  @override
  void write(BinaryWriter writer, LogCategory obj) {
    switch (obj) {
      case LogCategory.account:
        writer.writeByte(0);
        break;
      case LogCategory.post:
        writer.writeByte(1);
        break;
      case LogCategory.proxy:
        writer.writeByte(2);
        break;
      case LogCategory.scheduler:
        writer.writeByte(3);
        break;
      case LogCategory.system:
        writer.writeByte(4);
        break;
      case LogCategory.automation:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LogCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
