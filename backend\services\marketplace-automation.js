const FacebookAutomation = require('./facebook-automation');
const path = require('path');
const fs = require('fs');

class MarketplaceAutomation extends FacebookAutomation {
    constructor() {
        super();
        this.marketplaceUrl = 'https://www.facebook.com/marketplace/create';
    }

    /**
     * الانتقال إلى صفحة إنشاء إعلان في Marketplace
     */
    async navigateToMarketplace() {
        try {
            console.log('🏪 Navigating to Facebook Marketplace...');
            
            if (!this.isLoggedIn) {
                throw new Error('Must be logged in before accessing Marketplace');
            }

            // الانتقال إلى صفحة إنشاء الإعلان
            await this.page.goto(this.marketplaceUrl, {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            // انتظار تحميل الصفحة
            await this.page.waitForLoadState('domcontentloaded');
            
            // التحقق من وجود عناصر إنشاء الإعلان
            const createFormSelectors = [
                '[data-testid="marketplace-create-listing"]',
                'input[placeholder*="title"], input[placeholder*="عنوان"]',
                'textarea[placeholder*="description"], textarea[placeholder*="وصف"]',
                '[data-testid="marketplace-listing-title-input"]'
            ];

            let formFound = false;
            for (const selector of createFormSelectors) {
                try {
                    const element = await this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 10000 })) {
                        formFound = true;
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (!formFound) {
                throw new Error('Marketplace create form not found');
            }

            console.log('✅ Successfully navigated to Marketplace');
            return true;
        } catch (error) {
            console.error('❌ Failed to navigate to Marketplace:', error);
            
            // أخذ screenshot للتشخيص
            try {
                await this.page.screenshot({ 
                    path: path.join(__dirname, '../logs/marketplace-nav-error.png'),
                    fullPage: true 
                });
            } catch (e) {
                console.error('Failed to take screenshot:', e);
            }
            
            return false;
        }
    }

    /**
     * رفع الصور للإعلان
     */
    async uploadImages(imagePaths) {
        try {
            console.log(`📸 Uploading ${imagePaths.length} images...`);
            
            // البحث عن زر رفع الصور
            const uploadSelectors = [
                'input[type="file"][accept*="image"]',
                '[data-testid="marketplace-listing-photo-upload"]',
                'input[accept*="image/*"]'
            ];

            let uploadInput = null;
            for (const selector of uploadSelectors) {
                try {
                    uploadInput = await this.page.locator(selector).first();
                    if (await uploadInput.count() > 0) {
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (!uploadInput) {
                throw new Error('Image upload input not found');
            }

            // رفع الصور
            await uploadInput.setInputFiles(imagePaths);
            
            // انتظار رفع الصور
            await this.page.waitForTimeout(3000);
            
            // التحقق من رفع الصور بنجاح
            const uploadedImages = await this.page.locator('img[src*="blob:"], img[src*="data:"]').count();
            
            if (uploadedImages === 0) {
                throw new Error('No images were uploaded successfully');
            }

            console.log(`✅ Successfully uploaded ${uploadedImages} images`);
            return true;
        } catch (error) {
            console.error('❌ Failed to upload images:', error);
            return false;
        }
    }

    /**
     * تعبئة تفاصيل الإعلان
     */
    async fillListingDetails(listing) {
        try {
            console.log('📝 Filling listing details...');
            
            // تعبئة العنوان
            if (listing.title) {
                const titleSelectors = [
                    'input[placeholder*="title"], input[placeholder*="عنوان"]',
                    '[data-testid="marketplace-listing-title-input"]',
                    'input[name="title"]'
                ];

                for (const selector of titleSelectors) {
                    try {
                        const titleField = await this.page.locator(selector).first();
                        if (await titleField.isVisible()) {
                            await titleField.fill(listing.title);
                            await this.page.waitForTimeout(500);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }

            // تعبئة الوصف
            if (listing.description) {
                const descriptionSelectors = [
                    'textarea[placeholder*="description"], textarea[placeholder*="وصف"]',
                    '[data-testid="marketplace-listing-description-input"]',
                    'textarea[name="description"]'
                ];

                for (const selector of descriptionSelectors) {
                    try {
                        const descField = await this.page.locator(selector).first();
                        if (await descField.isVisible()) {
                            await descField.fill(listing.description);
                            await this.page.waitForTimeout(500);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }

            // تعبئة السعر
            if (listing.price) {
                const priceSelectors = [
                    'input[placeholder*="price"], input[placeholder*="سعر"]',
                    '[data-testid="marketplace-listing-price-input"]',
                    'input[name="price"]'
                ];

                for (const selector of priceSelectors) {
                    try {
                        const priceField = await this.page.locator(selector).first();
                        if (await priceField.isVisible()) {
                            await priceField.fill(listing.price.toString());
                            await this.page.waitForTimeout(500);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }

            // اختيار الفئة
            if (listing.category) {
                await this.selectCategory(listing.category);
            }

            // تعبئة الموقع
            if (listing.location) {
                await this.setLocation(listing.location);
            }

            console.log('✅ Listing details filled successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to fill listing details:', error);
            return false;
        }
    }

    /**
     * اختيار فئة المنتج
     */
    async selectCategory(category) {
        try {
            console.log(`🏷️ Selecting category: ${category}`);
            
            // البحث عن قائمة الفئات
            const categorySelectors = [
                '[data-testid="marketplace-listing-category-selector"]',
                'select[name="category"]',
                '[role="combobox"][aria-label*="category"], [role="combobox"][aria-label*="فئة"]'
            ];

            for (const selector of categorySelectors) {
                try {
                    const categoryField = await this.page.locator(selector).first();
                    if (await categoryField.isVisible()) {
                        await categoryField.click();
                        await this.page.waitForTimeout(1000);
                        
                        // البحث عن الفئة المطلوبة
                        const categoryOption = await this.page.locator(`text="${category}"`).first();
                        if (await categoryOption.isVisible()) {
                            await categoryOption.click();
                            await this.page.waitForTimeout(500);
                            return true;
                        }
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            console.log(`⚠️ Category "${category}" not found, using default`);
            return false;
        } catch (error) {
            console.error('❌ Failed to select category:', error);
            return false;
        }
    }

    /**
     * تحديد الموقع
     */
    async setLocation(location) {
        try {
            console.log(`📍 Setting location: ${location}`);
            
            const locationSelectors = [
                'input[placeholder*="location"], input[placeholder*="موقع"]',
                '[data-testid="marketplace-listing-location-input"]',
                'input[name="location"]'
            ];

            for (const selector of locationSelectors) {
                try {
                    const locationField = await this.page.locator(selector).first();
                    if (await locationField.isVisible()) {
                        await locationField.fill(location);
                        await this.page.waitForTimeout(1000);
                        
                        // اختيار أول اقتراح
                        const firstSuggestion = await this.page.locator('[role="option"]').first();
                        if (await firstSuggestion.isVisible({ timeout: 3000 })) {
                            await firstSuggestion.click();
                        }
                        
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }

            return false;
        } catch (error) {
            console.error('❌ Failed to set location:', error);
            return false;
        }
    }

    /**
     * نشر الإعلان
     */
    async publishListing() {
        try {
            console.log('🚀 Publishing listing...');
            
            // البحث عن زر النشر
            const publishSelectors = [
                'button[type="submit"]',
                'button:has-text("Publish"), button:has-text("نشر")',
                '[data-testid="marketplace-listing-publish-button"]'
            ];

            for (const selector of publishSelectors) {
                try {
                    const publishButton = await this.page.locator(selector).first();
                    if (await publishButton.isVisible()) {
                        await publishButton.click();
                        
                        // انتظار تأكيد النشر
                        await this.page.waitForTimeout(5000);
                        
                        // التحقق من نجاح النشر
                        const successIndicators = [
                            'text="Your listing is now live"',
                            'text="تم نشر إعلانك"',
                            '[data-testid="marketplace-listing-success"]'
                        ];

                        for (const indicator of successIndicators) {
                            try {
                                const successElement = await this.page.locator(indicator).first();
                                if (await successElement.isVisible({ timeout: 10000 })) {
                                    console.log('✅ Listing published successfully!');
                                    return { success: true, message: 'Listing published successfully' };
                                }
                            } catch (e) {
                                continue;
                            }
                        }

                        // إذا لم نجد تأكيد النجاح، نتحقق من عدم وجود أخطاء
                        const errorSelectors = [
                            '[role="alert"]',
                            '.error-message',
                            'text*="error", text*="خطأ"'
                        ];

                        for (const errorSelector of errorSelectors) {
                            try {
                                const errorElement = await this.page.locator(errorSelector).first();
                                if (await errorElement.isVisible()) {
                                    const errorText = await errorElement.textContent();
                                    return { success: false, message: errorText };
                                }
                            } catch (e) {
                                continue;
                            }
                        }

                        // إذا لم نجد أي تأكيد، نعتبر النشر ناجحاً
                        return { success: true, message: 'Listing appears to be published' };
                    }
                } catch (e) {
                    continue;
                }
            }

            throw new Error('Publish button not found');
        } catch (error) {
            console.error('❌ Failed to publish listing:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * إنشاء إعلان كامل
     */
    async createListing(listing, imagePaths = []) {
        try {
            console.log('🏪 Starting marketplace listing creation...');
            
            // التنقل إلى Marketplace
            const navigated = await this.navigateToMarketplace();
            if (!navigated) {
                throw new Error('Failed to navigate to Marketplace');
            }

            // رفع الصور إذا كانت متوفرة
            if (imagePaths && imagePaths.length > 0) {
                const imagesUploaded = await this.uploadImages(imagePaths);
                if (!imagesUploaded) {
                    console.log('⚠️ Failed to upload images, continuing without images');
                }
            }

            // تعبئة تفاصيل الإعلان
            const detailsFilled = await this.fillListingDetails(listing);
            if (!detailsFilled) {
                throw new Error('Failed to fill listing details');
            }

            // نشر الإعلان
            const publishResult = await this.publishListing();
            
            return publishResult;
        } catch (error) {
            console.error('❌ Failed to create listing:', error);
            
            // أخذ screenshot للتشخيص
            try {
                await this.page.screenshot({ 
                    path: path.join(__dirname, '../logs/listing-creation-error.png'),
                    fullPage: true 
                });
            } catch (e) {
                console.error('Failed to take screenshot:', e);
            }
            
            return { success: false, message: error.message };
        }
    }
}

module.exports = MarketplaceAutomation;
