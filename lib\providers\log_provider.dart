import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class LogProvider extends ChangeNotifier {
  final ApiService _apiService;
  
  List<LogEntry> _logs = [];
  bool _isLoading = false;
  String? _error;
  
  // فلاتر السجلات
  LogLevel? _selectedLevel;
  LogCategory? _selectedCategory;
  int? _selectedAccountId;
  int? _selectedPostId;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _searchQuery;

  List<LogEntry> get logs => _getFilteredLogs();
  List<LogEntry> get allLogs => _logs;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // الفلاتر
  LogLevel? get selectedLevel => _selectedLevel;
  LogCategory? get selectedCategory => _selectedCategory;
  int? get selectedAccountId => _selectedAccountId;
  int? get selectedPostId => _selectedPostId;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get searchQuery => _searchQuery;

  LogProvider(this._apiService) {
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    _setLoading(true);
    try {
      // تحميل من التخزين المحلي أولاً
      _logs = StorageService.getAllLogs();
      notifyListeners();

      // ثم المزامنة مع API
      await syncWithApi();
    } catch (e) {
      _setError('فشل في تحميل السجلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithApi() async {
    try {
      final response = await _apiService.getLogs(
        level: _selectedLevel?.name,
        category: _selectedCategory?.name,
        accountId: _selectedAccountId,
        postId: _selectedPostId,
        startDate: _startDate,
        endDate: _endDate,
        search: _searchQuery,
        limit: 100,
      );
      
      final List<dynamic> logsData = response['logs'];
      _logs = logsData.map((json) => LogEntry.fromJson(json)).toList();
      
      // حفظ في التخزين المحلي
      for (final log in _logs) {
        await StorageService.saveLog(log);
      }
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مزامنة السجلات: $e');
    }
  }

  Future<void> addLog(LogEntry log) async {
    try {
      final newLog = await _apiService.createLog(log);
      _logs.insert(0, newLog); // إضافة في المقدمة للأحدث أولاً
      await StorageService.saveLog(newLog);
      notifyListeners();
    } catch (e) {
      // في حالة فشل API، احفظ محلياً فقط
      _logs.insert(0, log);
      await StorageService.saveLog(log);
      notifyListeners();
    }
  }

  Future<void> clearLogs() async {
    try {
      await StorageService.clearLogs();
      _logs.clear();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح السجلات: $e');
    }
  }

  Future<void> clearLogsByLevel(LogLevel level) async {
    try {
      _logs.removeWhere((log) => log.level == level);
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح السجلات: $e');
    }
  }

  Future<void> clearLogsByCategory(LogCategory category) async {
    try {
      _logs.removeWhere((log) => log.category == category);
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح السجلات: $e');
    }
  }

  // تطبيق الفلاتر
  List<LogEntry> _getFilteredLogs() {
    List<LogEntry> filtered = List.from(_logs);

    if (_selectedLevel != null) {
      filtered = filtered.where((log) => log.level == _selectedLevel).toList();
    }

    if (_selectedCategory != null) {
      filtered = filtered.where((log) => log.category == _selectedCategory).toList();
    }

    if (_selectedAccountId != null) {
      filtered = filtered.where((log) => log.accountId == _selectedAccountId).toList();
    }

    if (_selectedPostId != null) {
      filtered = filtered.where((log) => log.postId == _selectedPostId).toList();
    }

    if (_startDate != null) {
      filtered = filtered.where((log) => log.timestamp.isAfter(_startDate!)).toList();
    }

    if (_endDate != null) {
      filtered = filtered.where((log) => log.timestamp.isBefore(_endDate!)).toList();
    }

    if (_searchQuery != null && _searchQuery!.isNotEmpty) {
      final query = _searchQuery!.toLowerCase();
      filtered = filtered.where((log) => 
          log.message.toLowerCase().contains(query) ||
          (log.details != null && log.details.toString().toLowerCase().contains(query))
      ).toList();
    }

    return filtered;
  }

  // تعيين الفلاتر
  void setLevelFilter(LogLevel? level) {
    _selectedLevel = level;
    notifyListeners();
  }

  void setCategoryFilter(LogCategory? category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void setAccountFilter(int? accountId) {
    _selectedAccountId = accountId;
    notifyListeners();
  }

  void setPostFilter(int? postId) {
    _selectedPostId = postId;
    notifyListeners();
  }

  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    notifyListeners();
  }

  void setSearchQuery(String? query) {
    _searchQuery = query;
    notifyListeners();
  }

  void clearFilters() {
    _selectedLevel = null;
    _selectedCategory = null;
    _selectedAccountId = null;
    _selectedPostId = null;
    _startDate = null;
    _endDate = null;
    _searchQuery = null;
    notifyListeners();
  }

  // الحصول على السجلات حسب المستوى
  List<LogEntry> getLogsByLevel(LogLevel level) {
    return _logs.where((log) => log.level == level).toList();
  }

  // الحصول على السجلات حسب الفئة
  List<LogEntry> getLogsByCategory(LogCategory category) {
    return _logs.where((log) => log.category == category).toList();
  }

  // إحصائيات السجلات
  int get totalLogs => _logs.length;
  int get errorLogsCount => getLogsByLevel(LogLevel.error).length;
  int get warningLogsCount => getLogsByLevel(LogLevel.warn).length;
  int get infoLogsCount => getLogsByLevel(LogLevel.info).length;
  int get debugLogsCount => getLogsByLevel(LogLevel.debug).length;

  // إحصائيات حسب الفئة
  int get accountLogsCount => getLogsByCategory(LogCategory.account).length;
  int get postLogsCount => getLogsByCategory(LogCategory.post).length;
  int get proxyLogsCount => getLogsByCategory(LogCategory.proxy).length;
  int get schedulerLogsCount => getLogsByCategory(LogCategory.scheduler).length;
  int get systemLogsCount => getLogsByCategory(LogCategory.system).length;
  int get automationLogsCount => getLogsByCategory(LogCategory.automation).length;

  // الحصول على آخر سجل خطأ
  LogEntry? get lastError {
    final errors = getLogsByLevel(LogLevel.error);
    return errors.isNotEmpty ? errors.first : null;
  }

  // الحصول على السجلات الحديثة (آخر 24 ساعة)
  List<LogEntry> getRecentLogs() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return _logs.where((log) => log.timestamp.isAfter(yesterday)).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
