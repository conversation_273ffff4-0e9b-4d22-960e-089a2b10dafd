{"version": 3, "file": "content-hcaptcha.js", "sourceRoot": "", "sources": ["../src/content-hcaptcha.ts"], "names": [], "mappings": ";;;AAEa,QAAA,wBAAwB,GAA4B;IAC/D,cAAc,EAAE,IAAI;CACrB,CAAA;AAEY,QAAA,wBAAwB,GAA4B;IAC/D,SAAS,EAAE,EAAE;CACd,CAAA;AAED;;;GAGG;AACH,MAAa,qBAAqB;IAShC,YACE,IAAI,GAAG,gCAAwB,EAC/B,IAAI,GAAG,gCAAwB;QAPzB,aAAQ,GAAG;YACjB,iCAAiC;YACjC,oCAAoC;SACrC,CAAA;QAMC,+DAA+D;QAC/D,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE;YAC5C,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAA;YAC5C,UAAU,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CACpC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;SACtE;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,OAAO,IAAI,OAAO,CAAC,UAAS,OAAO;YACjC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;gBAAE,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;YAC9C,MAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAC/D,IAAI,aAAa;gBAAE,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;YAEvC,SAAS,OAAO;gBACd,OAAO,CAAC,IAAI,CAAC,CAAA;gBACb,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;gBACzD,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YAC7C,CAAC;YAED,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YACtD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,iBAAiB,CAAC,OAA0B;QAClD,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC5B,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAiC,CAAA,CAAC,SAAS;aACnE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO;SACR;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,yBAAyB;IACjB,sBAAsB;QAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,gBAAgB,GAAG,qDAAqD,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC7G,CAAA;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7B,CAAC;IAED,sDAAsD;IAC9C,qBAAqB;QAC3B,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,sCAAsC,GAAG,0BAA0B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACxG,CAAA;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7B,CAAC;IAEO,uBAAuB,CAAC,OAA4B;QAC1D,OAAO,OAAO;aACX,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7C,GAAG,CAAC,GAAG,CAAC,EAAE;YACT,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;YACrC,MAAM,MAAM,GAAsB;gBAChC,OAAO,EAAE,UAAU;gBACnB,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBAC3B,EAAE,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;gBACpC,OAAO,EAAE;oBACP,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ;iBAC3C;aACF,CAAA;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,MAAM,MAAM,GAAG;YACb,QAAQ,EAAE,EAAyB;YACnC,KAAK,EAAE,IAAoB;SAC5B,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,MAAM,OAAO,GAAG;gBACd,GAAG,IAAI,CAAC,sBAAsB,EAAE;gBAChC,GAAG,IAAI,CAAC,qBAAqB,EAAE;aAChC,CAAA;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnB,OAAO,MAAM,CAAA;aACd;YACD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;YACvD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACnB,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;YAC5B,CAAC,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAEM,KAAK,CAAC,uBAAuB;QAClC,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,EAA2B;YACnC,KAAK,EAAE,IAAW;SACnB,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEpC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;YACrC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACnC,MAAM,CAAC,KAAK,GAAG,uBAAuB,CAAA;gBACtC,OAAO,MAAM,CAAA;aACd;YACD,MAAM,CAAC,MAAM,GAAG,SAAS;iBACtB,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,KAAK,UAAU,CAAC;iBACnD,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,CAAC;iBACjD,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACd,MAAM,CAAC,WAAW,CAChB,IAAI,CAAC,SAAS,CAAC;oBACb,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,KAAK,EAAE,kBAAkB;oBACzB,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE;wBACR,KAAK,EAAE,kBAAkB;wBACzB,UAAU,EAAE,GAAG;wBACf,QAAQ,EAAE,QAAQ,CAAC,IAAI;qBACxB;iBACF,CAAC,EACF,GAAG,CACJ,CAAA;gBACD,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI,IAAI,EAAE;iBACrB,CAAA;YACH,CAAC,CAAC,CAAA;SACL;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AA1JD,sDA0JC"}