import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../models/models.dart';
import '../providers/post_provider.dart';
import '../providers/account_provider.dart';
import '../utils/facebook_categories.dart';
import '../utils/app_localizations.dart';

class PostDialog extends StatefulWidget {
  final Post? post;

  const PostDialog({super.key, this.post});

  @override
  State<PostDialog> createState() => _PostDialogState();
}

class _PostDialogState extends State<PostDialog> {
  late final TextEditingController titleController;
  late final TextEditingController descriptionController;
  late final TextEditingController priceController;
  late final TextEditingController locationController;

  late String selectedCategory;
  List<String> selectedImagePaths = [];
  List<PlatformFile> selectedImageFiles = [];
  List<int> selectedAccountIds = [];

  @override
  void initState() {
    super.initState();
    titleController = TextEditingController(text: widget.post?.title ?? '');
    descriptionController = TextEditingController(text: widget.post?.description ?? '');
    priceController = TextEditingController(text: widget.post?.price.toString() ?? '');
    locationController = TextEditingController(text: widget.post?.location ?? '');
    selectedCategory = widget.post?.category ?? FacebookCategories.getDefaultCategory();
    selectedAccountIds = widget.post?.accountIds ?? [];
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    priceController.dispose();
    locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isEditing = widget.post != null;

    return AlertDialog(
      title: Text(isEditing ? 'تعديل المنشور' : localizations.addPost),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // حقل العنوان
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: 'العنوان',
                  prefixIcon: const Icon(Icons.title),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الوصف
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // حقل السعر
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  labelText: 'السعر',
                  prefixIcon: const Icon(Icons.attach_money),
                  suffixText: 'جنيه',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),

              // حقل الموقع
              TextField(
                controller: locationController,
                decoration: InputDecoration(
                  labelText: 'الموقع',
                  prefixIcon: const Icon(Icons.location_on),
                ),
              ),
              const SizedBox(height: 16),

              // قائمة الفئات
              DropdownButtonFormField<String>(
                value: selectedCategory,
                decoration: InputDecoration(
                  labelText: 'الفئة',
                  prefixIcon: const Icon(Icons.category),
                ),
                items: FacebookCategories.categories
                    .map((category) => DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedCategory = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // قسم اختيار الصور
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.image, color: Theme.of(context).colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        'الصور',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: _pickImages,
                        icon: const Icon(Icons.add_photo_alternate),
                        label: const Text('اختيار صور'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (selectedImageFiles.isNotEmpty)
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: selectedImageFiles.length,
                        itemBuilder: (context, index) {
                          final file = selectedImageFiles[index];
                          return Container(
                            margin: const EdgeInsets.only(right: 8),
                            child: Stack(
                              children: [
                                Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.grey.withOpacity(0.3)),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: file.bytes != null
                                        ? Image.memory(
                                            file.bytes!,
                                            fit: BoxFit.cover,
                                          )
                                        : const Icon(Icons.image, size: 50),
                                  ),
                                ),
                                Positioned(
                                  top: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  Text(
                    'تم اختيار ${selectedImageFiles.length} صورة',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // قسم اختيار الحسابات
              Consumer<AccountProvider>(
                builder: (context, accountProvider, child) {
                  final accounts = accountProvider.accounts;
                  
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_circle, color: Theme.of(context).colorScheme.primary),
                          const SizedBox(width: 8),
                          Text(
                            'اختيار الحسابات للنشر',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (accounts.isEmpty)
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning, color: Colors.orange),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'لا توجد حسابات متاحة. يرجى إضافة حساب أولاً.',
                                  style: TextStyle(color: Colors.orange[700]),
                                ),
                              ),
                            ],
                          ),
                        )
                      else
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.withOpacity(0.3)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: accounts.map((account) {
                              final isSelected = selectedAccountIds.contains(account.id);
                              return CheckboxListTile(
                                title: Text(account.name),
                                subtitle: Text(account.email),
                                value: isSelected,
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedAccountIds.add(account.id!);
                                    } else {
                                      selectedAccountIds.remove(account.id);
                                    }
                                  });
                                },
                                secondary: CircleAvatar(
                                  backgroundColor: account.status == AccountStatus.active 
                                      ? Colors.green 
                                      : Colors.grey,
                                  child: Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      const SizedBox(height: 8),
                      Text(
                        'تم اختيار ${selectedAccountIds.length} حساب',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(localizations.cancel),
        ),
        ElevatedButton(
          onPressed: () => _savePost(context),
          child: Text(localizations.save),
        ),
      ],
    );
  }

  Future<void> _pickImages() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        withData: true,
      );

      if (result != null) {
        setState(() {
          selectedImageFiles.addAll(result.files);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار الصور: $e')),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      selectedImageFiles.removeAt(index);
    });
  }

  void _savePost(BuildContext context) {
    if (titleController.text.isEmpty || 
        descriptionController.text.isEmpty || 
        priceController.text.isEmpty || 
        selectedCategory.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جميع الحقول الأساسية مطلوبة')),
      );
      return;
    }

    final price = double.tryParse(priceController.text);
    if (price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال سعر صحيح')),
      );
      return;
    }

    final provider = Provider.of<PostProvider>(context, listen: false);

    if (widget.post != null) {
      // تعديل منشور موجود
      final updatedPost = widget.post!.copyWith(
        title: titleController.text,
        description: descriptionController.text,
        price: price,
        category: selectedCategory,
        location: locationController.text,
        accountIds: selectedAccountIds,
      );
      provider.updatePost(updatedPost);
    } else {
      // إضافة منشور جديد
      final List<PostImage> postImages = [];

      // إنشاء PostImage من الملفات المختارة
      for (int i = 0; i < selectedImageFiles.length; i++) {
        final file = selectedImageFiles[i];
        final fileName = file.name;
        postImages.add(PostImage(
          filename: fileName,
          originalName: fileName,
          path: fileName,
          size: file.bytes?.length ?? 0,
          isUrl: false,
        ));
      }

      final newPost = Post(
        title: titleController.text,
        description: descriptionController.text,
        price: price,
        category: selectedCategory,
        location: locationController.text,
        status: PostStatus.draft,
        accountIds: selectedAccountIds,
        images: postImages,
      );
      provider.addPost(newPost, platformFiles: selectedImageFiles);
    }

    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.post != null ? 'تم تحديث المنشور بنجاح' : 'تم إضافة المنشور بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
