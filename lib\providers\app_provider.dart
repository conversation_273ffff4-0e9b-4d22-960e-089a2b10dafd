import 'package:flutter/material.dart';
import '../services/storage_service.dart';

class App<PERSON><PERSON>ider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  Locale _locale = const Locale('ar', 'SA');
  bool _isRTL = true;

  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  bool get isRTL => _isRTL;

  AppProvider() {
    _loadSettings();
  }

  void _loadSettings() {
    // Load theme mode
    final themeIndex = StorageService.getSetting<int>('theme_mode', defaultValue: 0);
    _themeMode = ThemeMode.values[themeIndex!];

    // Load locale
    final languageCode = StorageService.getSetting<String>('language_code', defaultValue: 'ar');
    final countryCode = StorageService.getSetting<String>('country_code', defaultValue: 'SA');
    _locale = Locale(languageCode!, countryCode!);
    _isRTL = languageCode == 'ar';

    notifyListeners();
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await StorageService.saveSetting('theme_mode', mode.index);
    notifyListeners();
  }

  Future<void> setLocale(Locale locale) async {
    _locale = locale;
    _isRTL = locale.languageCode == 'ar';
    await StorageService.saveSetting('language_code', locale.languageCode);
    await StorageService.saveSetting('country_code', locale.countryCode);
    notifyListeners();
  }

  Future<void> toggleLanguage() async {
    if (_locale.languageCode == 'ar') {
      await setLocale(const Locale('en', 'US'));
    } else {
      await setLocale(const Locale('ar', 'SA'));
    }
  }
}
