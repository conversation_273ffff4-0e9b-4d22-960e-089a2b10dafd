import 'package:hive/hive.dart';

part 'proxy.g.dart';

@HiveType(typeId: 5)
class ProxyModel extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String host;

  @HiveField(3)
  int port;

  @HiveField(4)
  ProxyType type;

  @HiveField(5)
  String? encryptedCredentials;

  @HiveField(6)
  ProxyStatus status;

  @HiveField(7)
  DateTime? lastTested;

  @HiveField(8)
  String? responseTime;

  @HiveField(9)
  DateTime createdAt;

  @HiveField(10)
  bool isActive;

  @HiveField(11)
  String? lastError;

  @HiveField(12)
  String? currentIp;

  ProxyModel({
    this.id,
    required this.name,
    required this.host,
    required this.port,
    required this.type,
    this.encryptedCredentials,
    this.status = ProxyStatus.untested,
    this.lastTested,
    this.responseTime,
    DateTime? createdAt,
    this.isActive = true,
    this.lastError,
    this.currentIp,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'host': host,
      'port': port,
      'type': type.name,
      'status': status.name,
      'lastTested': lastTested?.toIso8601String(),
      'responseTime': responseTime,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
      'lastError': lastError,
      'currentIp': currentIp,
    };
  }

  // Create from JSON response
  factory ProxyModel.fromJson(Map<String, dynamic> json) {
    return ProxyModel(
      id: json['id'],
      name: json['name'],
      host: json['host'],
      port: json['port'],
      type: ProxyType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ProxyType.http,
      ),
      status: ProxyStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ProxyStatus.untested,
      ),
      lastTested: json['lastTested'] != null 
          ? DateTime.parse(json['lastTested']) 
          : null,
      responseTime: json['responseTime'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      isActive: json['isActive'] ?? true,
      lastError: json['lastError'],
      currentIp: json['currentIp'],
    );
  }

  // Copy with method for updates
  ProxyModel copyWith({
    int? id,
    String? name,
    String? host,
    int? port,
    ProxyType? type,
    String? encryptedCredentials,
    ProxyStatus? status,
    DateTime? lastTested,
    String? responseTime,
    DateTime? createdAt,
    bool? isActive,
    String? lastError,
    String? currentIp,
  }) {
    return ProxyModel(
      id: id ?? this.id,
      name: name ?? this.name,
      host: host ?? this.host,
      port: port ?? this.port,
      type: type ?? this.type,
      encryptedCredentials: encryptedCredentials ?? this.encryptedCredentials,
      status: status ?? this.status,
      lastTested: lastTested ?? this.lastTested,
      responseTime: responseTime ?? this.responseTime,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      lastError: lastError ?? this.lastError,
      currentIp: currentIp ?? this.currentIp,
    );
  }

  @override
  String toString() {
    return 'ProxyModel(id: $id, name: $name, host: $host:$port, status: $status)';
  }
}

@HiveType(typeId: 6)
enum ProxyType {
  @HiveField(0)
  http,

  @HiveField(1)
  https,

  @HiveField(2)
  socks4,

  @HiveField(3)
  socks5,
}

@HiveType(typeId: 7)
enum ProxyStatus {
  @HiveField(0)
  untested,

  @HiveField(1)
  active,

  @HiveField(2)
  failed,

  @HiveField(3)
  testing,
}

extension ProxyTypeExtension on ProxyType {
  String get displayName {
    switch (this) {
      case ProxyType.http:
        return 'HTTP';
      case ProxyType.https:
        return 'HTTPS';
      case ProxyType.socks4:
        return 'SOCKS4';
      case ProxyType.socks5:
        return 'SOCKS5';
    }
  }
}

extension ProxyStatusExtension on ProxyStatus {
  String get displayName {
    switch (this) {
      case ProxyStatus.untested:
        return 'غير مختبر';
      case ProxyStatus.active:
        return 'نشط';
      case ProxyStatus.failed:
        return 'فشل';
      case ProxyStatus.testing:
        return 'قيد الاختبار';
    }
  }

  String get displayNameEn {
    switch (this) {
      case ProxyStatus.untested:
        return 'Untested';
      case ProxyStatus.active:
        return 'Active';
      case ProxyStatus.failed:
        return 'Failed';
      case ProxyStatus.testing:
        return 'Testing';
    }
  }
}
