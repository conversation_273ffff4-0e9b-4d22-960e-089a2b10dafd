import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class PostProvider extends ChangeNotifier {
  final ApiService _apiService;
  
  List<Post> _posts = [];
  bool _isLoading = false;
  String? _error;

  List<Post> get posts => _posts;
  bool get isLoading => _isLoading;
  String? get error => _error;

  PostProvider(this._apiService) {
    _loadPosts();
  }

  Future<void> _loadPosts() async {
    _setLoading(true);
    try {
      // Load from local storage first
      _posts = StorageService.getAllPosts();
      notifyListeners();

      // Then sync with API
      await syncWithApi();
    } catch (e) {
      _setError('Failed to load posts: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithApi() async {
    try {
      final apiPosts = await _apiService.getPosts();
      _posts = apiPosts;

      // Save to local storage
      for (final post in _posts) {
        await StorageService.savePost(post);
      }

      _clearError();
      notifyListeners();
    } catch (e) {
      // في حالة فشل الاتصال، استخدم البيانات المحلية
      print('Failed to sync posts with API: $e');
      _posts = StorageService.getAllPosts();
      _setError('تعذر الاتصال بالخادم. يتم عرض البيانات المحفوظة محلياً.');
      notifyListeners();
    }
  }

  Future<void> addPost(Post post, {List<File>? imageFiles, List<PlatformFile>? platformFiles}) async {
    _setLoading(true);
    try {
      final newPost = await _apiService.createPost(post, imageFiles: imageFiles);
      _posts.add(newPost);
      await StorageService.savePost(newPost);
      _clearError();
      notifyListeners();
    } catch (e) {
      // If API fails, save locally with generated ID
      final localPost = post.copyWith(
        id: DateTime.now().millisecondsSinceEpoch,
        createdAt: DateTime.now(),
      );
      _posts.add(localPost);
      await StorageService.savePost(localPost);
      _setError('تم حفظ المنشور محلياً. سيتم مزامنته مع الخادم عند توفر الاتصال.');
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updatePost(Post post, {List<File>? imageFiles}) async {
    _setLoading(true);
    try {
      final updatedPost = await _apiService.updatePost(post, imageFiles: imageFiles);
      final index = _posts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        _posts[index] = updatedPost;
        await StorageService.savePost(updatedPost);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update post: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deletePost(int postId) async {
    _setLoading(true);
    try {
      await _apiService.deletePost(postId);
      _posts.removeWhere((p) => p.id == postId);
      await StorageService.deletePost(postId);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete post: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> publishPost(int postId) async {
    try {
      final result = await _apiService.publishPost(postId);
      
      // Update post status
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final updatedPost = post.copyWith(
          status: result['success'] ? PostStatus.published : PostStatus.failed,
          publishedAt: result['success'] ? DateTime.now() : null,
        );
        _posts[postIndex] = updatedPost;
        await StorageService.savePost(updatedPost);
        notifyListeners();
      }
      
      return result['success'] ?? false;
    } catch (e) {
      _setError('Failed to publish post: $e');
      return false;
    }
  }

  Future<void> schedulePost(int postId, DateTime scheduledAt) async {
    _setLoading(true);
    try {
      final updatedPost = await _apiService.schedulePost(postId, scheduledAt);
      final index = _posts.indexWhere((p) => p.id == postId);
      if (index != -1) {
        _posts[index] = updatedPost;
        await StorageService.savePost(updatedPost);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to schedule post: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> cancelSchedule(int postId) async {
    _setLoading(true);
    try {
      final updatedPost = await _apiService.cancelSchedule(postId);
      final index = _posts.indexWhere((p) => p.id == postId);
      if (index != -1) {
        _posts[index] = updatedPost;
        await StorageService.savePost(updatedPost);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to cancel schedule: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> saveAsTemplate(int postId, String templateName) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final post = _posts[postIndex];
        final templatePost = post.copyWith(
          isTemplate: true,
          templateName: templateName,
        );
        _posts[postIndex] = templatePost;
        await StorageService.savePost(templatePost);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to save template: $e');
    }
  }

  Post? getPostById(int id) {
    try {
      return _posts.firstWhere((post) => post.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Post> getPostsByStatus(PostStatus status) {
    return _posts.where((post) => post.status == status).toList();
  }

  List<Post> getTemplates() {
    return _posts.where((post) => post.isTemplate).toList();
  }

  List<Post> getScheduledPosts() {
    return _posts.where((post) => 
        post.status == PostStatus.scheduled && 
        post.scheduledAt != null).toList();
  }

  List<Post> getPostsByAccount(int accountId) {
    return _posts.where((post) => 
        post.accountIds.contains(accountId)).toList();
  }

  int get totalPosts => _posts.length;
  int get draftPostsCount => getPostsByStatus(PostStatus.draft).length;
  int get scheduledPostsCount => getPostsByStatus(PostStatus.scheduled).length;
  int get publishedPostsCount => getPostsByStatus(PostStatus.published).length;
  int get failedPostsCount => getPostsByStatus(PostStatus.failed).length;
  int get templatesCount => getTemplates().length;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
