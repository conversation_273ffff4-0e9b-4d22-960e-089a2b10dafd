import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class AccountProvider extends ChangeNotifier {
  final ApiService _apiService;
  
  List<Account> _accounts = [];
  bool _isLoading = false;
  String? _error;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;
  String? get error => _error;

  AccountProvider(this._apiService) {
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    _setLoading(true);
    try {
      // Load from local storage first
      _accounts = StorageService.getAllAccounts();
      notifyListeners();

      // Then sync with API
      await syncWithApi();
    } catch (e) {
      _setError('Failed to load accounts: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithApi() async {
    try {
      final apiAccounts = await _apiService.getAccounts();
      _accounts = apiAccounts;

      // Save to local storage
      for (final account in _accounts) {
        await StorageService.saveAccount(account);
      }

      _clearError();
      notifyListeners();
    } catch (e) {
      // If API fails, load from local storage
      _accounts = StorageService.getAllAccounts();
      _setError('تعذر الاتصال بالخادم. يتم عرض البيانات المحفوظة محلياً.');
      notifyListeners();
    }
  }

  Future<void> addAccount(Account account, [String? password]) async {
    _setLoading(true);
    try {
      final newAccount = await _apiService.createAccount(account, password);
      _accounts.add(newAccount);
      await StorageService.saveAccount(newAccount);
      _clearError();
      notifyListeners();
    } catch (e) {
      // If API fails, save locally with generated ID
      final localAccount = account.copyWith(
        id: DateTime.now().millisecondsSinceEpoch,
        createdAt: DateTime.now(),
      );
      _accounts.add(localAccount);
      await StorageService.saveAccount(localAccount);
      _setError('تم حفظ الحساب محلياً. سيتم مزامنته مع الخادم عند توفر الاتصال.');
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateAccount(Account account, [String? password]) async {
    _setLoading(true);
    try {
      final updatedAccount = await _apiService.updateAccount(account, password);
      final index = _accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        _accounts[index] = updatedAccount;
        await StorageService.saveAccount(updatedAccount);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update account: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteAccount(int accountId) async {
    _setLoading(true);
    try {
      await _apiService.deleteAccount(accountId);
      _accounts.removeWhere((a) => a.id == accountId);
      await StorageService.deleteAccount(accountId);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete account: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<Map<String, dynamic>> testAccount(int accountId) async {
    try {
      final result = await _apiService.testAccount(accountId);

      // Update account status based on test result
      final accountIndex = _accounts.indexWhere((a) => a.id == accountId);
      if (accountIndex != -1) {
        final account = _accounts[accountIndex];
        final updatedAccount = account.copyWith(
          status: result['success'] ? AccountStatus.active : AccountStatus.error,
          lastLogin: result['success'] ? DateTime.now() : null,
        );
        _accounts[accountIndex] = updatedAccount;
        await StorageService.saveAccount(updatedAccount);
        notifyListeners();
      }

      return result;
    } catch (e) {
      _setError('Failed to test account: $e');
      return {
        'success': false,
        'message': 'فشل في اختبار الحساب: $e',
        'details': {},
        'facebookStatus': 'error',
      };
    }
  }

  Account? getAccountById(int id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Account> getActiveAccounts() {
    return _accounts.where((account) => 
        account.isActive && account.status == AccountStatus.active).toList();
  }

  List<Account> getAccountsByStatus(AccountStatus status) {
    return _accounts.where((account) => account.status == status).toList();
  }

  int get totalAccounts => _accounts.length;
  int get activeAccountsCount => getActiveAccounts().length;
  int get inactiveAccountsCount => 
      _accounts.where((a) => a.status == AccountStatus.inactive).length;
  int get bannedAccountsCount => 
      _accounts.where((a) => a.status == AccountStatus.banned).length;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
