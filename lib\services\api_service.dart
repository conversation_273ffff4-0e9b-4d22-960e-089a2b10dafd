import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../models/models.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors for logging in debug mode
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }
  }

  // Generic error handler
  String _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['error'] ?? 'Unknown error occurred';
        return 'Server error ($statusCode): $message';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check if the backend server is running.';
      default:
        return 'An unexpected error occurred: ${error.message}';
    }
  }

  // Account API methods
  Future<List<Account>> getAccounts() async {
    try {
      final response = await _dio.get('/accounts');
      final List<dynamic> data = response.data;
      return data.map((json) => Account.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Account> createAccount(Account account, [String? password]) async {
    try {
      final data = account.toJson();
      if (password != null) {
        data['password'] = password;
      }
      final response = await _dio.post('/accounts', data: data);
      return Account.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Account> updateAccount(Account account, [String? password]) async {
    try {
      final data = account.toJson();
      if (password != null) {
        data['password'] = password;
      }
      final response = await _dio.put('/accounts/${account.id}', data: data);
      return Account.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<void> deleteAccount(int accountId) async {
    try {
      await _dio.delete('/accounts/$accountId');
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Map<String, dynamic>> testAccount(int accountId) async {
    try {
      final response = await _dio.post('/accounts/$accountId/test');
      return response.data;
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  // Post API methods
  Future<List<Post>> getPosts() async {
    try {
      final response = await _dio.get('/posts');
      final List<dynamic> data = response.data;
      return data.map((json) => Post.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Post> createPost(Post post, {List<File>? imageFiles, List<PlatformFile>? platformFiles}) async {
    try {
      print('ApiService.createPost called');
      print('Platform files count: ${platformFiles?.length ?? 0}');

      FormData formData = FormData();

      // Add post data
      formData.fields.addAll([
        MapEntry('title', post.title),
        MapEntry('description', post.description),
        MapEntry('price', post.price.toString()),
        MapEntry('category', post.category),
        MapEntry('location', post.location),
        MapEntry('accountIds', jsonEncode(post.accountIds)),
      ]);

      // Add image files from File objects
      if (imageFiles != null) {
        print('Adding ${imageFiles.length} image files');
        for (File file in imageFiles) {
          formData.files.add(MapEntry(
            'images',
            await MultipartFile.fromFile(file.path),
          ));
        }
      }

      // Add image files from PlatformFile objects (for web)
      if (platformFiles != null) {
        print('Adding ${platformFiles.length} platform files');
        for (PlatformFile file in platformFiles) {
          if (file.bytes != null) {
            print('Adding file: ${file.name}, size: ${file.bytes!.length}');
            formData.files.add(MapEntry(
              'images',
              MultipartFile.fromBytes(
                file.bytes!,
                filename: file.name,
              ),
            ));
          }
        }
      }

      print('Total files in FormData: ${formData.files.length}');
      final response = await _dio.post('/posts', data: formData);
      return Post.fromJson(response.data);
    } on DioException catch (e) {
      print('Error in createPost: $e');
      throw Exception(_handleError(e));
    }
  }

  Future<Post> updatePost(Post post, {List<File>? imageFiles, List<PlatformFile>? platformFiles}) async {
    try {
      FormData formData = FormData();

      // Add post data
      formData.fields.addAll([
        MapEntry('title', post.title),
        MapEntry('description', post.description),
        MapEntry('price', post.price.toString()),
        MapEntry('category', post.category),
        MapEntry('location', post.location),
        MapEntry('accountIds', jsonEncode(post.accountIds)),
      ]);

      // Add image files from File objects
      if (imageFiles != null) {
        for (File file in imageFiles) {
          formData.files.add(MapEntry(
            'images',
            await MultipartFile.fromFile(file.path),
          ));
        }
      }

      // Add image files from PlatformFile objects (for web)
      if (platformFiles != null) {
        for (PlatformFile file in platformFiles) {
          if (file.bytes != null) {
            formData.files.add(MapEntry(
              'images',
              MultipartFile.fromBytes(
                file.bytes!,
                filename: file.name,
              ),
            ));
          }
        }
      }

      final response = await _dio.put('/posts/${post.id}', data: formData);
      return Post.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<void> deletePost(int postId) async {
    try {
      await _dio.delete('/posts/$postId');
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Map<String, dynamic>> publishPost(int postId) async {
    try {
      final response = await _dio.post('/posts/$postId/publish');
      return response.data;
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Post> schedulePost(int postId, DateTime scheduledAt) async {
    try {
      final response = await _dio.post('/posts/$postId/schedule', data: {
        'scheduledAt': scheduledAt.toIso8601String(),
      });
      return Post.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Post> cancelSchedule(int postId) async {
    try {
      // استخدام endpoint تعديل المنشور لإلغاء الجدولة
      final response = await _dio.put('/posts/$postId', data: {
        'status': 'draft',
        'scheduledAt': null,
      });
      return Post.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  // Proxy API methods
  Future<List<ProxyModel>> getProxies() async {
    try {
      final response = await _dio.get('/proxies');
      final List<dynamic> data = response.data;
      return data.map((json) => ProxyModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<ProxyModel> createProxy(ProxyModel proxy) async {
    try {
      final response = await _dio.post('/proxies', data: proxy.toJson());
      return ProxyModel.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<ProxyModel> updateProxy(ProxyModel proxy) async {
    try {
      final response = await _dio.put('/proxies/${proxy.id}', data: proxy.toJson());
      return ProxyModel.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<void> deleteProxy(int proxyId) async {
    try {
      await _dio.delete('/proxies/$proxyId');
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Map<String, dynamic>> testProxy(int proxyId) async {
    try {
      final response = await _dio.post('/proxies/$proxyId/test');
      return response.data;
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  // Schedule API methods
  Future<List<Schedule>> getSchedules() async {
    try {
      final response = await _dio.get('/scheduler');
      final List<dynamic> data = response.data;
      return data.map((json) => Schedule.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Schedule> createSchedule(Schedule schedule) async {
    try {
      final response = await _dio.post('/scheduler', data: schedule.toJson());
      return Schedule.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Schedule> updateSchedule(Schedule schedule) async {
    try {
      final response = await _dio.put('/scheduler/${schedule.id}', data: schedule.toJson());
      return Schedule.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<void> deleteSchedule(int scheduleId) async {
    try {
      await _dio.delete('/scheduler/$scheduleId');
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<Schedule> toggleSchedule(int scheduleId) async {
    try {
      final response = await _dio.post('/scheduler/$scheduleId/toggle');
      return Schedule.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  // Log API methods
  Future<Map<String, dynamic>> getLogs({
    String? level,
    String? category,
    int? accountId,
    int? postId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 50,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (level != null) queryParams['level'] = level;
      if (category != null) queryParams['category'] = category;
      if (accountId != null) queryParams['accountId'] = accountId;
      if (postId != null) queryParams['postId'] = postId;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();
      if (search != null) queryParams['search'] = search;

      final response = await _dio.get('/logs', queryParameters: queryParams);
      return response.data;
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }

  Future<LogEntry> createLog(LogEntry log) async {
    try {
      final response = await _dio.post('/logs', data: log.toJson());
      return LogEntry.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(_handleError(e));
    }
  }
}
