import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  bool get isArabic => locale.languageCode == 'ar';

  // النصوص العامة
  String get appTitle => isArabic ? 'أتمتة فيسبوك ماركت بليس' : 'Facebook Marketplace Automation';
  String get loading => isArabic ? 'جاري التحميل...' : 'Loading...';
  String get error => isArabic ? 'خطأ' : 'Error';
  String get success => isArabic ? 'نجح' : 'Success';
  String get cancel => isArabic ? 'إلغاء' : 'Cancel';
  String get save => isArabic ? 'حفظ' : 'Save';
  String get delete => isArabic ? 'حذف' : 'Delete';
  String get edit => isArabic ? 'تعديل' : 'Edit';
  String get add => isArabic ? 'إضافة' : 'Add';
  String get search => isArabic ? 'بحث' : 'Search';
  String get filter => isArabic ? 'فلتر' : 'Filter';
  String get refresh => isArabic ? 'تحديث' : 'Refresh';
  String get settings => isArabic ? 'الإعدادات' : 'Settings';

  // التنقل
  String get dashboard => isArabic ? 'لوحة التحكم' : 'Dashboard';
  String get accounts => isArabic ? 'الحسابات' : 'Accounts';
  String get posts => isArabic ? 'المنشورات' : 'Posts';
  String get proxies => isArabic ? 'البروكسيات' : 'Proxies';
  String get scheduler => isArabic ? 'الجدولة' : 'Scheduler';
  String get logs => isArabic ? 'السجلات' : 'Logs';

  // الحسابات
  String get addAccount => isArabic ? 'إضافة حساب' : 'Add Account';
  String get accountName => isArabic ? 'اسم الحساب' : 'Account Name';
  String get email => isArabic ? 'البريد الإلكتروني' : 'Email';
  String get password => isArabic ? 'كلمة المرور' : 'Password';
  String get accountStatus => isArabic ? 'حالة الحساب' : 'Account Status';
  String get lastLogin => isArabic ? 'آخر تسجيل دخول' : 'Last Login';
  String get testAccount => isArabic ? 'اختبار الحساب' : 'Test Account';
  String get activeAccounts => isArabic ? 'الحسابات النشطة' : 'Active Accounts';
  String get inactiveAccounts => isArabic ? 'الحسابات غير النشطة' : 'Inactive Accounts';
  String get bannedAccounts => isArabic ? 'الحسابات المحظورة' : 'Banned Accounts';

  // المنشورات
  String get addPost => isArabic ? 'إضافة منشور' : 'Add Post';
  String get postTitle => isArabic ? 'عنوان المنشور' : 'Post Title';
  String get postDescription => isArabic ? 'وصف المنشور' : 'Post Description';
  String get price => isArabic ? 'السعر' : 'Price';
  String get category => isArabic ? 'الفئة' : 'Category';
  String get location => isArabic ? 'الموقع' : 'Location';
  String get images => isArabic ? 'الصور' : 'Images';
  String get publishPost => isArabic ? 'نشر المنشور' : 'Publish Post';
  String get schedulePost => isArabic ? 'جدولة المنشور' : 'Schedule Post';
  String get saveAsTemplate => isArabic ? 'حفظ كقالب' : 'Save as Template';
  String get templates => isArabic ? 'القوالب' : 'Templates';
  String get draftPosts => isArabic ? 'المسودات' : 'Draft Posts';
  String get scheduledPosts => isArabic ? 'المنشورات المجدولة' : 'Scheduled Posts';
  String get publishedPosts => isArabic ? 'المنشورات المنشورة' : 'Published Posts';

  // البروكسيات
  String get addProxy => isArabic ? 'إضافة بروكسي' : 'Add Proxy';
  String get proxyName => isArabic ? 'اسم البروكسي' : 'Proxy Name';
  String get host => isArabic ? 'المضيف' : 'Host';
  String get port => isArabic ? 'المنفذ' : 'Port';
  String get proxyType => isArabic ? 'نوع البروكسي' : 'Proxy Type';
  String get username => isArabic ? 'اسم المستخدم' : 'Username';
  String get testProxy => isArabic ? 'اختبار البروكسي' : 'Test Proxy';
  String get testAllProxies => isArabic ? 'اختبار جميع البروكسيات' : 'Test All Proxies';
  String get activeProxies => isArabic ? 'البروكسيات النشطة' : 'Active Proxies';
  String get failedProxies => isArabic ? 'البروكسيات الفاشلة' : 'Failed Proxies';

  // الجدولة
  String get addSchedule => isArabic ? 'إضافة جدولة' : 'Add Schedule';
  String get scheduleName => isArabic ? 'اسم الجدولة' : 'Schedule Name';
  String get startDate => isArabic ? 'تاريخ البداية' : 'Start Date';
  String get endDate => isArabic ? 'تاريخ النهاية' : 'End Date';
  String get frequency => isArabic ? 'التكرار' : 'Frequency';
  String get interval => isArabic ? 'الفترة' : 'Interval';
  String get randomDelay => isArabic ? 'التأخير العشوائي' : 'Random Delay';
  String get nextRun => isArabic ? 'التشغيل التالي' : 'Next Run';
  String get lastRun => isArabic ? 'آخر تشغيل' : 'Last Run';
  String get runCount => isArabic ? 'عدد مرات التشغيل' : 'Run Count';
  String get runNow => isArabic ? 'تشغيل الآن' : 'Run Now';
  String get activeSchedules => isArabic ? 'الجدولات النشطة' : 'Active Schedules';
  String get upcomingSchedules => isArabic ? 'الجدولات القادمة' : 'Upcoming Schedules';

  // السجلات
  String get logLevel => isArabic ? 'مستوى السجل' : 'Log Level';
  String get logCategory => isArabic ? 'فئة السجل' : 'Log Category';
  String get message => isArabic ? 'الرسالة' : 'Message';
  String get timestamp => isArabic ? 'الوقت' : 'Timestamp';
  String get clearLogs => isArabic ? 'مسح السجلات' : 'Clear Logs';
  String get exportLogs => isArabic ? 'تصدير السجلات' : 'Export Logs';
  String get errorLogs => isArabic ? 'سجلات الأخطاء' : 'Error Logs';
  String get warningLogs => isArabic ? 'سجلات التحذيرات' : 'Warning Logs';
  String get infoLogs => isArabic ? 'سجلات المعلومات' : 'Info Logs';

  // الحالات
  String get active => isArabic ? 'نشط' : 'Active';
  String get inactive => isArabic ? 'غير نشط' : 'Inactive';
  String get banned => isArabic ? 'محظور' : 'Banned';
  String get suspended => isArabic ? 'معلق' : 'Suspended';
  String get testing => isArabic ? 'قيد الاختبار' : 'Testing';
  String get failed => isArabic ? 'فشل' : 'Failed';
  String get draft => isArabic ? 'مسودة' : 'Draft';
  String get scheduled => isArabic ? 'مجدول' : 'Scheduled';
  String get publishing => isArabic ? 'قيد النشر' : 'Publishing';
  String get published => isArabic ? 'منشور' : 'Published';

  // الرسائل
  String get accountAddedSuccessfully => isArabic ? 'تم إضافة الحساب بنجاح' : 'Account added successfully';
  String get accountUpdatedSuccessfully => isArabic ? 'تم تحديث الحساب بنجاح' : 'Account updated successfully';
  String get accountDeletedSuccessfully => isArabic ? 'تم حذف الحساب بنجاح' : 'Account deleted successfully';
  String get postAddedSuccessfully => isArabic ? 'تم إضافة المنشور بنجاح' : 'Post added successfully';
  String get postPublishedSuccessfully => isArabic ? 'تم نشر المنشور بنجاح' : 'Post published successfully';
  String get proxyTestSuccessful => isArabic ? 'اختبار البروكسي نجح' : 'Proxy test successful';
  String get proxyTestFailed => isArabic ? 'اختبار البروكسي فشل' : 'Proxy test failed';
  String get scheduleCreatedSuccessfully => isArabic ? 'تم إنشاء الجدولة بنجاح' : 'Schedule created successfully';

  // التحقق من الصحة
  String get fieldRequired => isArabic ? 'هذا الحقل مطلوب' : 'This field is required';
  String get invalidEmail => isArabic ? 'بريد إلكتروني غير صحيح' : 'Invalid email';
  String get passwordTooShort => isArabic ? 'كلمة المرور قصيرة جداً' : 'Password too short';
  String get invalidPort => isArabic ? 'منفذ غير صحيح' : 'Invalid port';
  String get invalidPrice => isArabic ? 'سعر غير صحيح' : 'Invalid price';

  // الإعدادات
  String get language => isArabic ? 'اللغة' : 'Language';
  String get theme => isArabic ? 'المظهر' : 'Theme';
  String get lightTheme => isArabic ? 'فاتح' : 'Light';
  String get darkTheme => isArabic ? 'داكن' : 'Dark';
  String get systemTheme => isArabic ? 'النظام' : 'System';
  String get arabic => isArabic ? 'العربية' : 'Arabic';
  String get english => isArabic ? 'الإنجليزية' : 'English';
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
