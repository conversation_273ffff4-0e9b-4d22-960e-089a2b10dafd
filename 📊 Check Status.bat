@echo off
title Facebook Marketplace Automation - Status Check
color 0E

echo.
echo ╔═══════════════════════════════════════════════════════════╗
echo ║                                                           ║
echo ║                📊 STATUS CHECK                            ║
echo ║                                                           ║
echo ║            Facebook Marketplace Automation                ║
echo ║                                                           ║
echo ╚═══════════════════════════════════════════════════════════╝
echo.

echo 📊 Checking service status...
echo.

powershell -ExecutionPolicy Bypass -File "launcher.ps1" -Status

echo.
pause
