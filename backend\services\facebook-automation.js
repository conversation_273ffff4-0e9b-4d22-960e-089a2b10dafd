const { chromium } = require('playwright-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const RecaptchaPlugin = require('puppeteer-extra-plugin-recaptcha');
const path = require('path');
const fs = require('fs');

// إضافة plugins للتخفي من كشف الأتمتة
chromium.use(StealthPlugin());
chromium.use(RecaptchaPlugin({
    provider: {
        id: '2captcha',
        token: process.env.TWOCAPTCHA_TOKEN || null
    },
    visualFeedback: true
}));

class FacebookAutomation {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isLoggedIn = false;
        this.sessionPath = path.join(__dirname, '../sessions');
        
        // إنشاء مجلد الجلسات إذا لم يكن موجوداً
        if (!fs.existsSync(this.sessionPath)) {
            fs.mkdirSync(this.sessionPath, { recursive: true });
        }
    }

    /**
     * تشغيل المتصفح مع إعدادات متقدمة لتجنب الكشف
     */
    async launchBrowser(proxy = null) {
        try {
            console.log('🚀 Launching browser with stealth mode...');
            
            const launchOptions = {
                headless: false, // نبدأ بـ visible للتشخيص
                viewport: { width: 1366, height: 768 },
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
            };

            // إضافة البروكسي إذا كان متوفراً
            if (proxy) {
                launchOptions.args.push(`--proxy-server=${proxy.host}:${proxy.port}`);
            }

            this.browser = await chromium.launch(launchOptions);
            
            // إنشاء context مع إعدادات محسنة
            this.context = await this.browser.newContext({
                viewport: { width: 1366, height: 768 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                locale: 'ar-EG',
                timezoneId: 'Africa/Cairo',
                permissions: ['geolocation', 'notifications'],
                geolocation: { latitude: 30.0444, longitude: 31.2357 }, // القاهرة
                extraHTTPHeaders: {
                    'Accept-Language': 'ar-EG,ar;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            });

            // إضافة authentication للبروكسي إذا كان مطلوباً
            if (proxy && proxy.username && proxy.password) {
                await this.context.setHTTPCredentials({
                    username: proxy.username,
                    password: proxy.password
                });
            }

            this.page = await this.context.newPage();
            
            // إضافة معالجات للأخطاء والتنبيهات
            this.page.on('dialog', async dialog => {
                console.log(`📢 Dialog detected: ${dialog.message()}`);
                await dialog.accept();
            });

            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log(`❌ Page error: ${msg.text()}`);
                }
            });

            console.log('✅ Browser launched successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to launch browser:', error);
            return false;
        }
    }

    /**
     * تحميل Facebook مع معالجة الأخطاء المتقدمة
     */
    async loadFacebook() {
        try {
            console.log('🌐 Loading Facebook...');
            
            // محاولة تحميل الصفحة مع timeout مطول
            const response = await this.page.goto('https://www.facebook.com', {
                waitUntil: 'networkidle',
                timeout: 60000
            });

            if (!response) {
                throw new Error('No response received from Facebook');
            }

            console.log(`📊 Response status: ${response.status()}`);
            
            if (response.status() !== 200) {
                throw new Error(`Facebook returned status ${response.status()}`);
            }

            // انتظار تحميل العناصر الأساسية
            await this.page.waitForLoadState('domcontentloaded');
            
            // التحقق من وجود عناصر تسجيل الدخول
            const loginElements = await this.page.locator('input[name="email"], input[data-testid="royal_email"]').count();
            
            if (loginElements === 0) {
                // قد نكون مسجلين دخول بالفعل
                const loggedInElements = await this.page.locator('[data-testid="search"], [aria-label="Facebook"]').count();
                if (loggedInElements > 0) {
                    console.log('✅ Already logged in to Facebook');
                    this.isLoggedIn = true;
                    return true;
                }
                throw new Error('Login elements not found on Facebook page');
            }

            console.log('✅ Facebook loaded successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to load Facebook:', error);
            
            // أخذ screenshot للتشخيص
            try {
                await this.page.screenshot({ 
                    path: path.join(__dirname, '../logs/facebook-load-error.png'),
                    fullPage: true 
                });
                console.log('📸 Screenshot saved for debugging');
            } catch (screenshotError) {
                console.error('Failed to take screenshot:', screenshotError);
            }
            
            return false;
        }
    }

    /**
     * تسجيل الدخول إلى Facebook
     */
    async login(email, password) {
        try {
            console.log(`🔐 Attempting to login with email: ${email}`);
            
            if (!this.page) {
                throw new Error('Browser not initialized');
            }

            // البحث عن حقل الإيميل بطرق متعددة
            let emailField = null;
            const emailSelectors = [
                'input[name="email"]',
                'input[data-testid="royal_email"]',
                'input[type="email"]',
                '#email'
            ];

            for (const selector of emailSelectors) {
                try {
                    emailField = await this.page.locator(selector).first();
                    if (await emailField.isVisible()) {
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (!emailField) {
                throw new Error('Email field not found');
            }

            // تعبئة الإيميل
            await emailField.fill(email);
            await this.page.waitForTimeout(1000 + Math.random() * 1000); // تأخير عشوائي

            // البحث عن حقل كلمة المرور
            let passwordField = null;
            const passwordSelectors = [
                'input[name="pass"]',
                'input[data-testid="royal_pass"]',
                'input[type="password"]',
                '#pass'
            ];

            for (const selector of passwordSelectors) {
                try {
                    passwordField = await this.page.locator(selector).first();
                    if (await passwordField.isVisible()) {
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (!passwordField) {
                throw new Error('Password field not found');
            }

            // تعبئة كلمة المرور
            await passwordField.fill(password);
            await this.page.waitForTimeout(1000 + Math.random() * 1000);

            // البحث عن زر تسجيل الدخول
            let loginButton = null;
            const loginSelectors = [
                'button[name="login"]',
                'button[data-testid="royal_login_button"]',
                'button[type="submit"]',
                'input[type="submit"]'
            ];

            for (const selector of loginSelectors) {
                try {
                    loginButton = await this.page.locator(selector).first();
                    if (await loginButton.isVisible()) {
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (!loginButton) {
                throw new Error('Login button not found');
            }

            // النقر على زر تسجيل الدخول
            await loginButton.click();
            
            console.log('🔄 Login form submitted, waiting for response...');
            
            // انتظار التنقل أو ظهور رسالة خطأ
            console.log('🔄 Waiting for login response...');
            await this.page.waitForTimeout(3000);

            // انتظار تغيير URL أو ظهور عناصر تسجيل الدخول
            try {
                await this.page.waitForFunction(() => {
                    return window.location.href !== 'https://www.facebook.com/' ||
                           document.querySelector('[data-testid="search"]') ||
                           document.querySelector('[aria-label="Facebook"]') ||
                           document.querySelector('[data-testid="royal_login_error"]');
                }, { timeout: 15000 });
            } catch (e) {
                console.log('⏰ Timeout waiting for login response, continuing...');
            }

            // التحقق من نجاح تسجيل الدخول
            const currentUrl = this.page.url();
            console.log(`📍 Current URL: ${currentUrl}`);

            // البحث عن علامات نجاح تسجيل الدخول مع timeout أطول
            const successIndicators = [
                '[data-testid="search"]',
                '[aria-label="Facebook"]',
                '[data-testid="fb-feed"]',
                '.x1n2onr6', // Facebook feed container
                '[role="main"]',
                '[data-testid="nav-search-input"]',
                'div[role="banner"]'
            ];

            let loginSuccess = false;
            console.log('🔍 Checking for login success indicators...');

            for (const indicator of successIndicators) {
                try {
                    console.log(`   Checking: ${indicator}`);
                    const element = await this.page.locator(indicator).first();
                    const isVisible = await element.isVisible({ timeout: 8000 });
                    if (isVisible) {
                        console.log(`   ✅ Found: ${indicator}`);
                        loginSuccess = true;
                        break;
                    }
                } catch (e) {
                    console.log(`   ❌ Not found: ${indicator}`);
                    continue;
                }
            }

            if (loginSuccess) {
                console.log('✅ Login successful!');
                this.isLoggedIn = true;

                // تأكيد إضافي - انتظار تحميل الصفحة بالكامل
                console.log('🔄 Waiting for page to fully load...');
                await this.page.waitForTimeout(3000);

                // التحقق من إمكانية الوصول للعناصر الأساسية
                try {
                    await this.page.waitForLoadState('networkidle', { timeout: 10000 });
                    console.log('✅ Page fully loaded');
                } catch (e) {
                    console.log('⚠️ Page still loading, but login appears successful');
                }

                return { success: true, message: 'Login successful', verified: true };
            }

            // التحقق من وجود رسائل خطأ
            const errorSelectors = [
                '[data-testid="royal_login_error"]',
                '.x1yztbdb', // Facebook error message
                '[role="alert"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const errorElement = await this.page.locator(selector).first();
                    if (await errorElement.isVisible()) {
                        const errorText = await errorElement.textContent();
                        console.log(`❌ Login error: ${errorText}`);
                        return { success: false, message: errorText || 'Login failed' };
                    }
                } catch (e) {
                    continue;
                }
            }

            // التحقق من CAPTCHA
            const captchaSelectors = [
                '[data-testid="captcha"]',
                '.captcha',
                'iframe[src*="captcha"]'
            ];

            for (const selector of captchaSelectors) {
                try {
                    const captchaElement = await this.page.locator(selector).first();
                    if (await captchaElement.isVisible()) {
                        console.log('🤖 CAPTCHA detected, attempting to solve...');
                        
                        console.log('🤖 CAPTCHA detected - attempting automatic solution...');

                        // محاولة حل CAPTCHA تلقائياً
                        try {
                            await this.page.solveRecaptchas();
                            console.log('🔄 CAPTCHA solution attempted, waiting for result...');
                            await this.page.waitForTimeout(5000);

                            // إعادة التحقق من نجاح تسجيل الدخول بعد CAPTCHA
                            console.log('🔍 Re-checking login status after CAPTCHA...');
                            for (const indicator of successIndicators) {
                                try {
                                    const element = await this.page.locator(indicator).first();
                                    if (await element.isVisible({ timeout: 8000 })) {
                                        console.log('✅ Login successful after CAPTCHA!');
                                        this.isLoggedIn = true;

                                        // تأكيد إضافي بعد حل CAPTCHA
                                        await this.page.waitForTimeout(3000);
                                        await this.page.waitForLoadState('networkidle', { timeout: 10000 });

                                        return {
                                            success: true,
                                            message: 'Login successful after CAPTCHA',
                                            captchaSolved: true,
                                            verified: true
                                        };
                                    }
                                } catch (e) {
                                    continue;
                                }
                            }

                            // إذا لم ينجح بعد حل CAPTCHA
                            console.log('❌ Login still failed after CAPTCHA attempt');
                            return {
                                success: false,
                                message: 'CAPTCHA solved but login still failed',
                                requiresCaptcha: true,
                                keepBrowserOpen: true
                            };

                        } catch (captchaError) {
                            console.log('❌ Failed to solve CAPTCHA automatically:', captchaError.message);
                            return {
                                success: false,
                                message: 'CAPTCHA detected - manual intervention required',
                                requiresCaptcha: true,
                                keepBrowserOpen: true,
                                captchaError: captchaError.message
                            };
                        }
                    }
                } catch (e) {
                    continue;
                }
            }

            return { success: false, message: 'Login failed - unknown reason' };

        } catch (error) {
            console.error('❌ Login error:', error);
            
            // أخذ screenshot للتشخيص
            try {
                await this.page.screenshot({ 
                    path: path.join(__dirname, '../logs/login-error.png'),
                    fullPage: true 
                });
            } catch (e) {
                console.error('Failed to take screenshot:', e);
            }
            
            return { success: false, message: error.message };
        }
    }

    /**
     * التحقق المتقدم من حالة تسجيل الدخول
     */
    async verifyLoginStatus() {
        try {
            console.log('🔍 Performing advanced login verification...');

            // التحقق من URL الحالي
            const currentUrl = this.page.url();
            console.log(`📍 Current URL: ${currentUrl}`);

            // إذا كنا في صفحة تسجيل الدخول، فالتسجيل فاشل
            if (currentUrl.includes('/login') || currentUrl === 'https://www.facebook.com/') {
                return { verified: false, reason: 'Still on login page' };
            }

            // التحقق من وجود عناصر تسجيل الدخول
            const loginElements = await this.page.locator('input[name="email"], input[name="pass"]').count();
            if (loginElements > 0) {
                return { verified: false, reason: 'Login form still visible' };
            }

            // التحقق من وجود عناصر Facebook الرئيسية
            const mainElements = [
                '[data-testid="search"]',
                '[aria-label="Facebook"]',
                '[role="main"]',
                'div[role="banner"]'
            ];

            for (const selector of mainElements) {
                try {
                    const element = await this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 3000 })) {
                        console.log(`✅ Verified login with element: ${selector}`);
                        return { verified: true, element: selector };
                    }
                } catch (e) {
                    continue;
                }
            }

            // محاولة التفاعل مع الصفحة للتأكد من أنها تعمل
            try {
                await this.page.evaluate(() => document.title);
                const title = await this.page.title();
                console.log(`📄 Page title: ${title}`);

                if (title.includes('Facebook') && !title.includes('Log in')) {
                    return { verified: true, method: 'page_title', title };
                }
            } catch (e) {
                console.log('❌ Could not evaluate page');
            }

            return { verified: false, reason: 'No verification method succeeded' };
        } catch (error) {
            console.error('❌ Error verifying login status:', error);
            return { verified: false, reason: error.message };
        }
    }

    /**
     * إغلاق المتصفح وتنظيف الموارد
     */
    async close() {
        try {
            if (this.page) {
                await this.page.close();
            }
            if (this.context) {
                await this.context.close();
            }
            if (this.browser) {
                await this.browser.close();
            }
            console.log('✅ Browser closed successfully');
        } catch (error) {
            console.error('❌ Error closing browser:', error);
        }
    }
}

module.exports = FacebookAutomation;
