{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,mEAA6D;AAM7D,uCAAkD;AAClD,yDAA0D;AAC1D,gEAAiD;AAEpC,QAAA,wBAAwB,GAA6B;IAChE;QACE,EAAE,EAAE,UAAU,CAAC,WAAW;QAC1B,EAAE,EAAE,UAAU,CAAC,YAAY;KAC5B;CACF,CAAA;AAED;;;GAGG;AACH,MAAa,6BAA8B,SAAQ,6CAAoB;IAGrE,YAAY,IAAkC;QAC5C,KAAK,CAAC,IAAI,CAAC,CAAA;QAkCb,8EAA8E;QACtE,qBAAgB,GAAG,YAAY,CAAA;QAlCrC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAEpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,YAAY,EAAE,KAAK;YACnB,mBAAmB,EAAE,KAAK;YAC1B,eAAe,EAAE,KAAK;YACtB,uBAAuB,EAAE,KAAK;SAC/B,CAAA;IACH,CAAC;IAED,IAAI,IAAI;QACN,OAAO,KAAK,CAAC,IAAW,CAAA;IAC1B,CAAC;IAED,IAAI,iBAAiB;QACnB,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,IAAI,CAAA;QACpC,OAAO;YACL,cAAc;YACd,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO;gBAC3C,CAAC,CAAC,IAAI,CAAC,gBAAgB;gBACvB,CAAC,CAAC,SAAS;SACd,CAAA;IACH,CAAC;IAKO,sBAAsB,CAC5B,MAA2B,EAC3B,EAAgD,EAChD,IAAU;QAEV,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QACtD,IAAI,YAAY,GAAG,gCAAsB,CAAC,QAAQ,EAAE,CAAA;QACpD,IAAI,UAAU,GAAG,wBAAwB,CAAA;QACzC,IAAI,MAAM,KAAK,UAAU,EAAE;YACzB,YAAY,GAAG,wCAAqB,CAAC,QAAQ,EAAE,CAAA;YAC/C,UAAU,GAAG,uBAAuB,CAAA;SACrC;QACD,4EAA4E;QAC5E,+EAA+E;QAC/E,6EAA6E;QAC7E,SAAS;QACT,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,UAAU,IAAI,CAAC,CAAA;QACxE,OAAO;qBACU,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;qBAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC;;QAEnD,YAAY;2BACO,UAAU;sBACf,EAAE;SACf,CAAA;IACP,CAAC;IAED,mGAAmG;IAC3F,iBAAiB,CAAC,aAAkC,EAAE;QAC5D,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAwB,EAAE,EAAE;YAC1D,IACE,CAAC,CAAC,KAAK,KAAK,WAAW;gBACvB,CAAC,CAAC,CAAC,uBAAuB;gBAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAClC;gBACA,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,yBAAyB,CAAA;aAC7C;YACD,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACrD,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,iBAAiB,CAAA;aACrC;YACD,IACE,CAAC,CAAC,KAAK,KAAK,UAAU;gBACtB,CAAC,CAAC,CAAC,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAC7B;gBACA,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,qBAAqB,CAAA;aACzC;YACD,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE;oBAC3D,EAAE,EAAE,CAAC,CAAC,EAAE;oBACR,MAAM,EAAE,CAAC,CAAC,cAAc;oBACxB,OAAO,EAAE,CAAC;iBACX,CAAC,CAAA;aACH;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QACF,OAAO;YACL,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAwB;YACjE,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;SAC1C,CAAA;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,IAAkB;QACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QAC5B,sEAAsE;QACtE,6DAA6D;QAC7D,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,CAAC,CACxC,2EAA2E,CAC5E,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAA;QAC5D,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;YACxD,MAAM,IAAI;iBACP,eAAe,CACd;;;;OAIH,EACG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,CACrC;iBACA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA,CAAC,gBAAgB;SACxE;QACD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,CAAC,CACvC,sCAAsC,CACvC,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAA;QAC1D,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;YAC3D,MAAM,IAAI,CAAC,eAAe,CACxB;;;;OAID,EACC,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,CACrC,CAAA;YACD,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA,CAAC,gBAAgB;SAC3E;QAED,MAAM,oBAAoB,GAAG,CAAC,OAAe,EAAE,IAAS,EAAE,EAAE;YAC1D,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACxC,CAAC,CAAA;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACnC,IAAI,gBAAgB,IAAI,IAAI,EAAE;gBAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAA;aACvE;SACF;QACD,kEAAkE;QAClE,MAAM,eAAe,GAA+B,CAAC,MAAM,IAAI,CAAC,QAAQ,CACtE,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC3D,CAAQ,CAAA;QACT,MAAM,cAAc,GAA+B,CAAC,MAAM,IAAI,CAAC,QAAQ,CACrE,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAC1D,CAAQ,CAAA;QAET,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QACtE,IAAI,CAAC,KAAK,CACR,mBAAmB,aAAa,CAAC,QAAQ,CAAC,MAAM,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,kCAAkC,CACvH,CAAA;QAED,MAAM,QAAQ,GAA+B;YAC3C,QAAQ,EAAE,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC;YACjE,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,KAAK,EAAE,eAAe,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;SACrD,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,QAA6B,EAC7B,QAAiC;QAEjC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;QACpE,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACzC,IACE,CAAC,QAAQ;YACT,CAAC,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAChE;YACA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;SACrE;QACD,IAAI,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAA;QACpB,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,eAAe,GAAG,gCAAwB,CAAC,IAAI,CACnD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,EAAE,CAClC,CAAA;YACD,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;gBAC3C,MAAM,IAAI,KAAK,CACb,yCAAyC,QAAQ,CAAC,EAAE,IAAI,CACzD,CAAA;aACF;YACD,EAAE,GAAG,eAAe,CAAC,EAAE,CAAA;SACxB;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,IAAI,CAC5B,IAAI,EACJ,QAAQ,EACR,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,IAAI,IAAI,EAAE,CACpB,CAAA;QACD,QAAQ,CAAC,KAAK;YACZ,QAAQ,CAAC,KAAK;gBACd,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAwB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QAClE,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAA;QAC7C,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC9B,OAAO,CAAC,IAAI,CACV,iFAAiF,EACjF,QAAQ,CAAC,KAAK,CACf,CAAA;SACF;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,IAAkB,EAClB,SAAkC;QAElC,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;QAEpD,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,CAAA;QACrE,MAAM,eAAe,GAAwC,YAAY;YACvE,CAAC,CAAE,CAAC,MAAM,IAAI,CAAC,QAAQ,CACnB,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,yBAAyB,EAAE;gBAClE,SAAS;aACV,CAAC,CACH,CAAS;YACZ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;QAClB,MAAM,WAAW,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,CAAA;QACnE,MAAM,cAAc,GAAwC,WAAW;YACrE,CAAC,CAAE,CAAC,MAAM,IAAI,CAAC,QAAQ,CACnB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,yBAAyB,EAAE;gBACjE,SAAS;aACV,CAAC,CACH,CAAS;YACZ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;QAElB,MAAM,QAAQ,GAAwC;YACpD,MAAM,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC;YAC7D,KAAK,EAAE,eAAe,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;SACrD,CAAA;QACD,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACvE,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAA;QAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,IAAkB;QAElB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAC7B,MAAM,QAAQ,GAAgC;YAC5C,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,IAAI;SACZ,CAAA;QACD,IAAI;YACF,gDAAgD;YAChD,4CAA4C;YAC5C,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,KAAK,EAAE,aAAa,EACrB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACnC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAC5B,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAE5B,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,MAAM,EACJ,SAAS,EACT,KAAK,EAAE,cAAc,EACtB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACvD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAA;gBAE9B,MAAM,EACJ,MAAM,EACN,KAAK,EAAE,WAAW,EACnB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;gBAChE,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAA;gBAExB,QAAQ,CAAC,KAAK,GAAG,aAAa,IAAI,cAAc,IAAI,WAAW,CAAA;aAChE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;SAClC;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;QACvC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAEO,iBAAiB,CAAC,IAAkB;QAC1C,IAAI,CAAC,cAAc,GAAG,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,qBAAqB,GAAG,KAAK,EAChC,QAA6B,EAC7B,QAAiC,EACjC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACnD,IAAI,CAAC,uBAAuB,GAAG,KAAK,EAAE,SAAkC,EAAE,EAAE,CAC1E,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAC/C,gDAAgD;QAChD,IAAI,CAAC,eAAe,GAAG,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;IAC/D,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAU;QAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;QACvC,0CAA0C;QAC1C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;QAE7B,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAE5B,iDAAiD;QACjD,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;YAC/B,IAAI,CAAC,KAAK;gBAAE,OAAM;YAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC/B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,yDAAyD;IACzD,KAAK,CAAC,SAAS,CAAC,OAAgB;QAC9B,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,KAAK,EAAE,CAAA;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;YAC5B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;gBAClD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;IACH,CAAC;CACF;AAzVD,sEAyVC;AAED,qDAAqD;AACrD,MAAM,aAAa,GAAG,CAAC,OAAsC,EAAE,EAAE;IAC/D,OAAO,IAAI,6BAA6B,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;AACzD,CAAC,CAAA;AAED,kBAAe,aAAa,CAAA"}