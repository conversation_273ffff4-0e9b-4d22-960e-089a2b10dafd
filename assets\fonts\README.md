# Cairo Font Files

## تعليمات تنزيل خطوط Cairo

1. اذهب إلى: https://fonts.google.com/specimen/Cairo
2. اضغط على "Download family"
3. استخرج الملفات المضغوطة
4. انسخ الملفات التالية إلى هذا المجلد:

### الملفات المطلوبة:
- `Cairo-Light.ttf` (weight: 300)
- `Cairo-Regular.ttf` (weight: 400)
- `Cairo-Medium.ttf` (weight: 500)
- `Cairo-SemiBold.ttf` (weight: 600)
- `Cairo-Bold.ttf` (weight: 700)

### أو استخدم الأمر التالي لتنزيل الخطوط:

```bash
# تنزيل خطوط Cairo من Google Fonts
curl -o Cairo.zip "https://fonts.google.com/download?family=Cairo"
unzip Cairo.zip -d cairo_fonts/
cp cairo_fonts/static/Cairo-Light.ttf assets/fonts/
cp cairo_fonts/static/Cairo-Regular.ttf assets/fonts/
cp cairo_fonts/static/Cairo-Medium.ttf assets/fonts/
cp cairo_fonts/static/Cairo-SemiBold.ttf assets/fonts/
cp cairo_fonts/static/Cairo-Bold.ttf assets/fonts/
```

### ملاحظة:
حالياً يستخدم التطبيق خطوط النظام كبديل. بعد إضافة خطوط Cairo الحقيقية، ستحتاج إلى:
1. تشغيل `flutter clean`
2. تشغيل `flutter pub get`
3. إعادة تشغيل التطبيق
