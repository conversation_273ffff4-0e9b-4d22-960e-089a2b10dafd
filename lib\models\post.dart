import 'package:hive/hive.dart';

part 'post.g.dart';

@HiveType(typeId: 2)
class Post extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String description;

  @HiveField(3)
  double price;

  @HiveField(4)
  String category;

  @HiveField(5)
  String location;

  @HiveField(6)
  List<PostImage> images;

  @HiveField(7)
  List<int> accountIds;

  @HiveField(8)
  PostStatus status;

  @HiveField(9)
  DateTime createdAt;

  @HiveField(10)
  DateTime? scheduledAt;

  @HiveField(11)
  DateTime? publishedAt;

  @HiveField(12)
  bool isTemplate;

  @HiveField(13)
  String? templateName;

  @HiveField(14)
  Map<String, dynamic>? metadata;

  Post({
    this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.category,
    this.location = '',
    List<PostImage>? images,
    List<int>? accountIds,
    this.status = PostStatus.draft,
    DateTime? createdAt,
    this.scheduledAt,
    this.publishedAt,
    this.isTemplate = false,
    this.templateName,
    this.metadata,
  }) : 
    images = images ?? [],
    accountIds = accountIds ?? [],
    createdAt = createdAt ?? DateTime.now();

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'category': category,
      'location': location,
      'images': images.map((img) => img.toJson()).toList(),
      'accountIds': accountIds,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'publishedAt': publishedAt?.toIso8601String(),
      'isTemplate': isTemplate,
      'templateName': templateName,
      'metadata': metadata,
    };
  }

  // Create from JSON response
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      price: (json['price'] as num).toDouble(),
      category: json['category'],
      location: json['location'] ?? '',
      images: (json['images'] as List?)
          ?.map((img) => PostImage.fromJson(img))
          .toList() ?? [],
      accountIds: List<int>.from(json['accountIds'] ?? []),
      status: PostStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PostStatus.draft,
      ),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      scheduledAt: json['scheduledAt'] != null 
          ? DateTime.parse(json['scheduledAt']) 
          : null,
      publishedAt: json['publishedAt'] != null 
          ? DateTime.parse(json['publishedAt']) 
          : null,
      isTemplate: json['isTemplate'] ?? false,
      templateName: json['templateName'],
      metadata: json['metadata'],
    );
  }

  // Copy with method for updates
  Post copyWith({
    int? id,
    String? title,
    String? description,
    double? price,
    String? category,
    String? location,
    List<PostImage>? images,
    List<int>? accountIds,
    PostStatus? status,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? publishedAt,
    bool? isTemplate,
    String? templateName,
    Map<String, dynamic>? metadata,
  }) {
    return Post(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      location: location ?? this.location,
      images: images ?? this.images,
      accountIds: accountIds ?? this.accountIds,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      publishedAt: publishedAt ?? this.publishedAt,
      isTemplate: isTemplate ?? this.isTemplate,
      templateName: templateName ?? this.templateName,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Post(id: $id, title: $title, status: $status)';
  }
}

@HiveType(typeId: 3)
class PostImage extends HiveObject {
  @HiveField(0)
  String filename;

  @HiveField(1)
  String originalName;

  @HiveField(2)
  String path;

  @HiveField(3)
  int size;

  @HiveField(4)
  bool isUrl;

  PostImage({
    required this.filename,
    required this.originalName,
    required this.path,
    required this.size,
    this.isUrl = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'filename': filename,
      'originalName': originalName,
      'path': path,
      'size': size,
      'isUrl': isUrl,
    };
  }

  factory PostImage.fromJson(Map<String, dynamic> json) {
    return PostImage(
      filename: json['filename'],
      originalName: json['originalName'],
      path: json['path'],
      size: json['size'],
      isUrl: json['isUrl'] ?? false,
    );
  }
}

@HiveType(typeId: 4)
enum PostStatus {
  @HiveField(0)
  draft,

  @HiveField(1)
  scheduled,

  @HiveField(2)
  publishing,

  @HiveField(3)
  published,

  @HiveField(4)
  failed,

  @HiveField(5)
  paused,
}

extension PostStatusExtension on PostStatus {
  String get displayName {
    switch (this) {
      case PostStatus.draft:
        return 'مسودة';
      case PostStatus.scheduled:
        return 'مجدول';
      case PostStatus.publishing:
        return 'قيد النشر';
      case PostStatus.published:
        return 'منشور';
      case PostStatus.failed:
        return 'فشل';
      case PostStatus.paused:
        return 'متوقف';
    }
  }

  String get displayNameEn {
    switch (this) {
      case PostStatus.draft:
        return 'Draft';
      case PostStatus.scheduled:
        return 'Scheduled';
      case PostStatus.publishing:
        return 'Publishing';
      case PostStatus.published:
        return 'Published';
      case PostStatus.failed:
        return 'Failed';
      case PostStatus.paused:
        return 'Paused';
    }
  }
}
