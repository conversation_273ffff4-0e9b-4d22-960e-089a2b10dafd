import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class ProxyProvider extends ChangeNotifier {
  final ApiService _apiService;
  
  List<ProxyModel> _proxies = [];
  bool _isLoading = false;
  String? _error;

  List<ProxyModel> get proxies => _proxies;
  bool get isLoading => _isLoading;
  String? get error => _error;

  ProxyProvider(this._apiService) {
    _loadProxies();
  }

  Future<void> _loadProxies() async {
    _setLoading(true);
    try {
      // تحميل من التخزين المحلي أولاً
      _proxies = StorageService.getAllProxies();
      notifyListeners();

      // ثم المزامنة مع API
      await syncWithApi();
    } catch (e) {
      _setError('فشل في تحميل البروكسيات: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithApi() async {
    try {
      final apiProxies = await _apiService.getProxies();
      _proxies = apiProxies;
      
      // حفظ في التخزين المحلي
      for (final proxy in _proxies) {
        await StorageService.saveProxy(proxy);
      }
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مزامنة البروكسيات: $e');
    }
  }

  Future<void> addProxy(ProxyModel proxy) async {
    _setLoading(true);
    try {
      final newProxy = await _apiService.createProxy(proxy);
      _proxies.add(newProxy);
      await StorageService.saveProxy(newProxy);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في إضافة البروكسي: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateProxy(ProxyModel proxy) async {
    _setLoading(true);
    try {
      final updatedProxy = await _apiService.updateProxy(proxy);
      final index = _proxies.indexWhere((p) => p.id == proxy.id);
      if (index != -1) {
        _proxies[index] = updatedProxy;
        await StorageService.saveProxy(updatedProxy);
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل في تحديث البروكسي: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteProxy(int proxyId) async {
    _setLoading(true);
    try {
      await _apiService.deleteProxy(proxyId);
      _proxies.removeWhere((p) => p.id == proxyId);
      await StorageService.deleteProxy(proxyId);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في حذف البروكسي: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> testProxy(int proxyId) async {
    try {
      // تحديث حالة البروكسي إلى "قيد الاختبار"
      final proxyIndex = _proxies.indexWhere((p) => p.id == proxyId);
      if (proxyIndex != -1) {
        final proxy = _proxies[proxyIndex];
        final testingProxy = proxy.copyWith(status: ProxyStatus.testing);
        _proxies[proxyIndex] = testingProxy;
        notifyListeners();
      }

      final result = await _apiService.testProxy(proxyId);
      
      // تحديث حالة البروكسي بناءً على نتيجة الاختبار
      if (proxyIndex != -1) {
        final proxy = _proxies[proxyIndex];
        final updatedProxy = proxy.copyWith(
          status: result['success'] ? ProxyStatus.active : ProxyStatus.failed,
          lastTested: DateTime.now(),
          responseTime: result['responseTime'],
          currentIp: result['ip'],
          lastError: result['success'] ? null : result['error'],
        );
        _proxies[proxyIndex] = updatedProxy;
        await StorageService.saveProxy(updatedProxy);
        notifyListeners();
      }
      
      return result['success'] ?? false;
    } catch (e) {
      _setError('فشل في اختبار البروكسي: $e');
      
      // إعادة تعيين حالة البروكسي في حالة الخطأ
      final proxyIndex = _proxies.indexWhere((p) => p.id == proxyId);
      if (proxyIndex != -1) {
        final proxy = _proxies[proxyIndex];
        final failedProxy = proxy.copyWith(
          status: ProxyStatus.failed,
          lastError: e.toString(),
        );
        _proxies[proxyIndex] = failedProxy;
        await StorageService.saveProxy(failedProxy);
        notifyListeners();
      }
      
      return false;
    }
  }

  Future<void> testAllProxies() async {
    _setLoading(true);
    try {
      for (final proxy in _proxies) {
        if (proxy.id != null) {
          await testProxy(proxy.id!);
        }
      }
      _clearError();
    } catch (e) {
      _setError('فشل في اختبار جميع البروكسيات: $e');
    } finally {
      _setLoading(false);
    }
  }

  ProxyModel? getProxyById(int id) {
    try {
      return _proxies.firstWhere((proxy) => proxy.id == id);
    } catch (e) {
      return null;
    }
  }

  List<ProxyModel> getActiveProxies() {
    return _proxies.where((proxy) => 
        proxy.isActive && proxy.status == ProxyStatus.active).toList();
  }

  List<ProxyModel> getProxiesByStatus(ProxyStatus status) {
    return _proxies.where((proxy) => proxy.status == status).toList();
  }

  List<ProxyModel> getProxiesByType(ProxyType type) {
    return _proxies.where((proxy) => proxy.type == type).toList();
  }

  ProxyModel? getRandomActiveProxy() {
    final activeProxies = getActiveProxies();
    if (activeProxies.isEmpty) return null;
    
    activeProxies.shuffle();
    return activeProxies.first;
  }

  int get totalProxies => _proxies.length;
  int get activeProxiesCount => getActiveProxies().length;
  int get failedProxiesCount => getProxiesByStatus(ProxyStatus.failed).length;
  int get untestedProxiesCount => getProxiesByStatus(ProxyStatus.untested).length;

  // إحصائيات حسب النوع
  int get httpProxiesCount => getProxiesByType(ProxyType.http).length;
  int get httpsProxiesCount => getProxiesByType(ProxyType.https).length;
  int get socks4ProxiesCount => getProxiesByType(ProxyType.socks4).length;
  int get socks5ProxiesCount => getProxiesByType(ProxyType.socks5).length;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
