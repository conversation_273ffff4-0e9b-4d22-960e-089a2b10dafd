{"version": 3, "file": "2captcha.js", "sourceRoot": "", "sources": ["../../src/provider/2captcha.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAa,QAAA,WAAW,GAAG,UAAU,CAAA;AAGrC,kDAAyB;AACzB,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,oCAAoC,mBAAW,EAAE,CAAC,CAAA;AAEtE,2CAA2C;AAC3C,uDAAwC;AAExC,MAAM,mBAAmB,GAAG,CAAC,MAAY,EAAE,KAAW,EAAE,EAAE,CACxD,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAA;AAa7C,MAAM,oBAAoB,GAA2B;IACnD,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,IAAI;CACrB,CAAA;AAED,KAAK,UAAU,oBAAoB,CACjC,KAAa,EACb,MAA2B,EAC3B,OAAe,EACf,GAAW,EACX,SAAc,EACd,IAAI,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE;IAEhC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,MAAM,EAAE,GAAG,CAAC,GAAQ,EAAE,MAAW,EAAE,OAAY,EAAE,EAAE,CACjD,OAAO,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAA;QACnC,IAAI;YACF,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAEvB,IAAI,MAAM,GAAG,eAAe,CAAA;YAC5B,IAAI,MAAM,KAAK,UAAU,EAAE;gBACzB,MAAM,GAAG,UAAU,CAAA;aACpB;YACD,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;SAClE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;SAC/B;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAEM,KAAK,UAAU,YAAY,CAChC,WAAgC,EAAE,EAClC,QAAgB,EAAE,EAClB,OAA+B,EAAE;IAEjC,IAAI,mCAAQ,oBAAoB,GAAK,IAAI,CAAE,CAAA;IAC3C,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAC/C,CAAA;IACD,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAA;AAC7D,CAAC;AAVD,oCAUC;AAED,KAAK,UAAU,WAAW,CACxB,OAA0B,EAC1B,KAAa,EACb,IAA4B;IAE5B,MAAM,QAAQ,GAA0B;QACtC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,QAAQ,EAAE,mBAAW;KACtB,CAAA;IACD,IAAI;QACF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;SAC3C;QACD,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAA;QACxB,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;QAC/B,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAA;QACxC,MAAM,SAAS,GAAG,EAAE,CAAA;QACpB,IAAI,OAAO,CAAC,CAAC,EAAE;YACb,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,CAAC,CAAA,CAAC,gCAAgC;SACjE;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,EAAE;YACzC,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA,CAAC,gCAAgC;SACtE;QACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,YAAY,EAAE;YAClD,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;SAC5B;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE;YAC5E,SAAS,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAA;YACzE,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;SAC9D;QAED,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,oBAAoB,CACzD,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,GAAG,EACX,SAAS,CACV,CAAA;QACD,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAA;QAC/C,IAAI,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,GAAG,mBAAW,WAAW,GAAG,EAAE,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,GAAG,mBAAW,kCAAkC,MAAM,EAAE,CAAC,CAAA;SAC1E;QACD,QAAQ,CAAC,iBAAiB,GAAG,MAAM,CAAC,EAAE,CAAA;QACtC,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;QAChC,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAA;QACtC,QAAQ,CAAC,QAAQ,GAAG,mBAAmB,CACrC,QAAQ,CAAC,SAAS,EAClB,QAAQ,CAAC,UAAU,CACpB,CAAA;KACF;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACrB,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;KAClC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC"}