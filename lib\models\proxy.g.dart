// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProxyModelAdapter extends TypeAdapter<ProxyModel> {
  @override
  final int typeId = 5;

  @override
  ProxyModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProxyModel(
      id: fields[0] as int?,
      name: fields[1] as String,
      host: fields[2] as String,
      port: fields[3] as int,
      type: fields[4] as ProxyType,
      encryptedCredentials: fields[5] as String?,
      status: fields[6] as ProxyStatus,
      lastTested: fields[7] as DateTime?,
      responseTime: fields[8] as String?,
      createdAt: fields[9] as DateTime?,
      isActive: fields[10] as bool,
      lastError: fields[11] as String?,
      currentIp: fields[12] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProxyModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.host)
      ..writeByte(3)
      ..write(obj.port)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.encryptedCredentials)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.lastTested)
      ..writeByte(8)
      ..write(obj.responseTime)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.isActive)
      ..writeByte(11)
      ..write(obj.lastError)
      ..writeByte(12)
      ..write(obj.currentIp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProxyModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProxyTypeAdapter extends TypeAdapter<ProxyType> {
  @override
  final int typeId = 6;

  @override
  ProxyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProxyType.http;
      case 1:
        return ProxyType.https;
      case 2:
        return ProxyType.socks4;
      case 3:
        return ProxyType.socks5;
      default:
        return ProxyType.http;
    }
  }

  @override
  void write(BinaryWriter writer, ProxyType obj) {
    switch (obj) {
      case ProxyType.http:
        writer.writeByte(0);
        break;
      case ProxyType.https:
        writer.writeByte(1);
        break;
      case ProxyType.socks4:
        writer.writeByte(2);
        break;
      case ProxyType.socks5:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProxyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProxyStatusAdapter extends TypeAdapter<ProxyStatus> {
  @override
  final int typeId = 7;

  @override
  ProxyStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProxyStatus.untested;
      case 1:
        return ProxyStatus.active;
      case 2:
        return ProxyStatus.failed;
      case 3:
        return ProxyStatus.testing;
      default:
        return ProxyStatus.untested;
    }
  }

  @override
  void write(BinaryWriter writer, ProxyStatus obj) {
    switch (obj) {
      case ProxyStatus.untested:
        writer.writeByte(0);
        break;
      case ProxyStatus.active:
        writer.writeByte(1);
        break;
      case ProxyStatus.failed:
        writer.writeByte(2);
        break;
      case ProxyStatus.testing:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProxyStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
