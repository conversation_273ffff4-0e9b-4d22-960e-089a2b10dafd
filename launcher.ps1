# Facebook Marketplace Automation - Professional Launcher
param(
    [switch]$Backend,
    [switch]$Frontend,
    [switch]$Both,
    [switch]$Stop,
    [switch]$Status
)

# Colors
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

# Configuration
$BackendPort = 3000

# Banner
function Show-Banner {
    Write-Host "================================================================" -ForegroundColor $Cyan
    Write-Host "    FACEBOOK MARKETPLACE AUTOMATION - LAUNCHER" -ForegroundColor $Cyan
    Write-Host "================================================================" -ForegroundColor $Cyan
}

# Check if port is in use
function Test-Port {
    param($Port)
    try {
        $Connection = New-Object System.Net.Sockets.TcpClient
        $Connection.Connect("localhost", $Port)
        $Connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Stop processes on port
function Stop-ProcessOnPort {
    param($Port)
    try {
        $Processes = netstat -ano | Select-String ":$Port " | ForEach-Object {
            $Fields = $_ -split '\s+'
            $Fields[4]
        }
        
        foreach ($PID in $Processes) {
            if ($PID -and $PID -ne "0") {
                Write-Host "Stopping process PID: $PID" -ForegroundColor $Yellow
                Stop-Process -Id $PID -Force -ErrorAction SilentlyContinue
            }
        }
    }
    catch {
        Write-Host "Error stopping processes: $_" -ForegroundColor $Red
    }
}

# Start Backend
function Start-Backend {
    Write-Host "Starting Backend Server..." -ForegroundColor $Yellow
    
    if (Test-Port $BackendPort) {
        Write-Host "Port $BackendPort is in use. Stopping existing process..." -ForegroundColor $Yellow
        Stop-ProcessOnPort $BackendPort
        Start-Sleep -Seconds 2
    }
    
    try {
        Set-Location "backend"
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "node server.js"
        
        # Wait for server to start
        $Counter = 0
        do {
            Start-Sleep -Seconds 1
            $Counter++
            Write-Host "." -NoNewline -ForegroundColor $Yellow
        } while (-not (Test-Port $BackendPort) -and $Counter -lt 30)
        
        Set-Location ".."
        
        if (Test-Port $BackendPort) {
            Write-Host "`nBackend started on port $BackendPort" -ForegroundColor $Green
            return $true
        } else {
            Write-Host "`nFailed to start backend" -ForegroundColor $Red
            return $false
        }
    }
    catch {
        Write-Host "`nError: $_" -ForegroundColor $Red
        Set-Location ".."
        return $false
    }
}

# Start Frontend
function Start-Frontend {
    Write-Host "Starting Flutter Frontend..." -ForegroundColor $Yellow
    
    try {
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "flutter run -d chrome"
        Write-Host "Flutter started" -ForegroundColor $Green
        return $true
    }
    catch {
        Write-Host "Error starting frontend: $_" -ForegroundColor $Red
        return $false
    }
}

# Stop all services
function Stop-AllServices {
    Write-Host "Stopping all services..." -ForegroundColor $Yellow
    
    if (Test-Port $BackendPort) {
        Stop-ProcessOnPort $BackendPort
    }
    
    try {
        Get-Process | Where-Object {$_.ProcessName -like "*flutter*" -or $_.ProcessName -like "*dart*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    }
    catch {}
    
    Write-Host "All services stopped" -ForegroundColor $Green
}

# Show status
function Show-Status {
    Write-Host "Service Status:" -ForegroundColor $Cyan
    
    if (Test-Port $BackendPort) {
        Write-Host "Backend: Running on port $BackendPort" -ForegroundColor $Green
    } else {
        Write-Host "Backend: Not running" -ForegroundColor $Red
    }
    
    $FlutterProcesses = Get-Process | Where-Object {$_.ProcessName -like "*flutter*" -or $_.ProcessName -like "*dart*"}
    if ($FlutterProcesses) {
        Write-Host "Flutter: Running" -ForegroundColor $Green
    } else {
        Write-Host "Flutter: Not running" -ForegroundColor $Red
    }
}

# Main
Show-Banner

if ($Stop) {
    Stop-AllServices
    exit
}

if ($Status) {
    Show-Status
    exit
}

$BackendSuccess = $false
$FrontendSuccess = $false

if ($Backend -or $Both -or (-not $Frontend)) {
    $BackendSuccess = Start-Backend
}

if ($Frontend -or $Both) {
    if ($Backend -or $Both) {
        Start-Sleep -Seconds 3
    }
    $FrontendSuccess = Start-Frontend
}

Write-Host "`n================================================================" -ForegroundColor $Cyan
Write-Host "SUMMARY:" -ForegroundColor $Cyan

if ($BackendSuccess) {
    Write-Host "Backend: http://localhost:$BackendPort" -ForegroundColor $Green
}

if ($FrontendSuccess) {
    Write-Host "Frontend: Started in new window" -ForegroundColor $Green
}

Write-Host "`nCommands:" -ForegroundColor $Yellow
Write-Host "  .\launcher.ps1 -Both     # Start both" -ForegroundColor $Yellow
Write-Host "  .\launcher.ps1 -Backend  # Backend only" -ForegroundColor $Yellow
Write-Host "  .\launcher.ps1 -Frontend # Frontend only" -ForegroundColor $Yellow
Write-Host "  .\launcher.ps1 -Stop     # Stop all" -ForegroundColor $Yellow
Write-Host "  .\launcher.ps1 -Status   # Check status" -ForegroundColor $Yellow
