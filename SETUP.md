# 🚀 دليل تشغيل تطبيق Facebook Marketplace Automation

## 📋 **المتطلبات الأساسية**

### 🛠️ **البرامج المطلوبة:**
- **Flutter SDK** (الإصدار 3.0 أو أحدث)
- **Dart SDK** (مضمن مع Flutter)
- **Node.js** (للـ Backend)
- **Chrome Browser** (للتشغيل على الويب)

### 📦 **التبعيات:**
```yaml
dependencies:
  flutter: sdk: flutter
  provider: ^6.1.1
  dio: ^5.4.0
  web_socket_channel: ^2.4.0
  idb_shim: ^2.4.1+1
  shared_preferences: ^2.2.2
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  flutter_localizations: sdk: flutter
  intl: any
```

## 🏃‍♂️ **خطوات التشغيل**

### 1️⃣ **تحضير المشروع:**
```bash
# استنساخ المشروع
git clone [repository-url]
cd marketplace-app

# تحديث التبعيات
flutter pub get

# تنظيف المشروع (اختياري)
flutter clean
flutter pub get
```

### 2️⃣ **تشغيل Backend:**
```bash
# الانتقال لمجلد Backend
cd backend

# تثبيت التبعيات
npm install

# تشغيل الخادم
npm start
# أو
node server.js
```

### 3️⃣ **تشغيل التطبيق:**
```bash
# العودة لمجلد التطبيق الرئيسي
cd ..

# تشغيل على Chrome
flutter run -d chrome

# أو تشغيل على Windows
flutter run -d windows

# أو تشغيل على أي جهاز متاح
flutter run
```

## 🎨 **إضافة خطوط Cairo**

### 📥 **تنزيل الخطوط:**
1. اذهب إلى: https://fonts.google.com/specimen/Cairo
2. اضغط على "Download family"
3. استخرج الملفات المضغوطة
4. انسخ الملفات التالية إلى `assets/fonts/`:
   - `Cairo-Light.ttf`
   - `Cairo-Regular.ttf`
   - `Cairo-Medium.ttf`
   - `Cairo-SemiBold.ttf`
   - `Cairo-Bold.ttf`

### 🔄 **تحديث التطبيق:**
```bash
flutter clean
flutter pub get
flutter run -d chrome
```

## 🌐 **عناوين الوصول**

### 📱 **التطبيق:**
- **Frontend:** http://localhost:62538 (أو المنفذ المعروض)
- **Backend API:** http://localhost:3000
- **WebSocket:** ws://localhost:3000

### 🔗 **API Endpoints:**
- `GET /api/accounts` - جلب الحسابات
- `POST /api/accounts` - إضافة حساب
- `GET /api/posts` - جلب المنشورات
- `POST /api/posts` - إضافة منشور
- `GET /api/proxies` - جلب البروكسيات
- `GET /api/scheduler` - جلب الجدولات
- `GET /api/logs` - جلب السجلات

## 🎯 **الميزات المتاحة**

### ✅ **جاهز للاستخدام:**
- 📊 **لوحة التحكم** مع إحصائيات شاملة
- 👥 **إدارة الحسابات** مع اختبار الاتصال
- 📝 **إدارة المنشورات** مع جدولة النشر
- 🔒 **إدارة البروكسيات** مع اختبار الأداء
- ⏰ **نظام الجدولة** للأتمتة
- 📋 **نظام السجلات** للمراقبة
- 🌙 **Dark Mode** مع تبديل سريع
- 🔤 **خط Cairo** للنصوص العربية

## 🔧 **استكشاف الأخطاء**

### ❌ **مشاكل شائعة:**

#### **1. خطأ في الاتصال بـ Backend:**
```bash
# تأكد من تشغيل Backend
cd backend
npm start
```

#### **2. خطأ في WebSocket:**
- تأكد من أن Backend يعمل على المنفذ 3000
- تحقق من إعدادات الجدار الناري

#### **3. خطأ في الخطوط:**
- تأكد من وجود ملفات Cairo في `assets/fonts/`
- شغل `flutter clean && flutter pub get`

#### **4. خطأ في قواعد البيانات:**
- امسح بيانات المتصفح (IndexedDB)
- أعد تشغيل التطبيق

## 📱 **المنصات المدعومة**

### ✅ **مختبر ويعمل:**
- **🌐 Web (Chrome)** - الأساسي
- **🖥️ Windows Desktop** - مدعوم
- **🍎 macOS** - مدعوم نظرياً
- **🐧 Linux** - مدعوم نظرياً

### 📱 **Mobile (قيد التطوير):**
- **📱 Android** - يحتاج تعديلات
- **🍎 iOS** - يحتاج تعديلات

## 🔐 **الأمان**

### 🛡️ **الميزات الأمنية:**
- تشفير البيانات الحساسة
- تخزين محلي آمن
- اتصال WebSocket مشفر
- التحقق من صحة البيانات

## 📞 **الدعم**

### 🆘 **في حالة وجود مشاكل:**
1. تحقق من ملف `FEATURES.md` للميزات المتاحة
2. راجع السجلات في التطبيق
3. تأكد من تشغيل Backend
4. أعد تشغيل التطبيق

**التطبيق جاهز للاستخدام! 🎉**
