import 'package:hive/hive.dart';

part 'log_entry.g.dart';

@HiveType(typeId: 10)
class LogEntry extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  DateTime timestamp;

  @HiveField(2)
  LogLevel level;

  @HiveField(3)
  LogCategory category;

  @HiveField(4)
  String message;

  @HiveField(5)
  Map<String, dynamic>? details;

  @HiveField(6)
  int? accountId;

  @HiveField(7)
  int? postId;

  LogEntry({
    this.id,
    DateTime? timestamp,
    required this.level,
    required this.category,
    required this.message,
    this.details,
    this.accountId,
    this.postId,
  }) : timestamp = timestamp ?? DateTime.now();

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'level': level.name,
      'category': category.name,
      'message': message,
      'details': details,
      'accountId': accountId,
      'postId': postId,
    };
  }

  // Create from JSON response
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      id: json['id'],
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp']) 
          : DateTime.now(),
      level: LogLevel.values.firstWhere(
        (e) => e.name == json['level'],
        orElse: () => LogLevel.info,
      ),
      category: LogCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => LogCategory.system,
      ),
      message: json['message'],
      details: json['details'],
      accountId: json['accountId'],
      postId: json['postId'],
    );
  }

  // Copy with method for updates
  LogEntry copyWith({
    int? id,
    DateTime? timestamp,
    LogLevel? level,
    LogCategory? category,
    String? message,
    Map<String, dynamic>? details,
    int? accountId,
    int? postId,
  }) {
    return LogEntry(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      level: level ?? this.level,
      category: category ?? this.category,
      message: message ?? this.message,
      details: details ?? this.details,
      accountId: accountId ?? this.accountId,
      postId: postId ?? this.postId,
    );
  }

  @override
  String toString() {
    return 'LogEntry(id: $id, level: $level, category: $category, message: $message)';
  }
}

@HiveType(typeId: 11)
enum LogLevel {
  @HiveField(0)
  debug,

  @HiveField(1)
  info,

  @HiveField(2)
  warn,

  @HiveField(3)
  error,
}

@HiveType(typeId: 12)
enum LogCategory {
  @HiveField(0)
  account,

  @HiveField(1)
  post,

  @HiveField(2)
  proxy,

  @HiveField(3)
  scheduler,

  @HiveField(4)
  system,

  @HiveField(5)
  automation,
}

extension LogLevelExtension on LogLevel {
  String get displayName {
    switch (this) {
      case LogLevel.debug:
        return 'تصحيح';
      case LogLevel.info:
        return 'معلومات';
      case LogLevel.warn:
        return 'تحذير';
      case LogLevel.error:
        return 'خطأ';
    }
  }

  String get displayNameEn {
    switch (this) {
      case LogLevel.debug:
        return 'Debug';
      case LogLevel.info:
        return 'Info';
      case LogLevel.warn:
        return 'Warning';
      case LogLevel.error:
        return 'Error';
    }
  }
}

extension LogCategoryExtension on LogCategory {
  String get displayName {
    switch (this) {
      case LogCategory.account:
        return 'حساب';
      case LogCategory.post:
        return 'منشور';
      case LogCategory.proxy:
        return 'بروكسي';
      case LogCategory.scheduler:
        return 'جدولة';
      case LogCategory.system:
        return 'نظام';
      case LogCategory.automation:
        return 'أتمتة';
    }
  }

  String get displayNameEn {
    switch (this) {
      case LogCategory.account:
        return 'Account';
      case LogCategory.post:
        return 'Post';
      case LogCategory.proxy:
        return 'Proxy';
      case LogCategory.scheduler:
        return 'Scheduler';
      case LogCategory.system:
        return 'System';
      case LogCategory.automation:
        return 'Automation';
    }
  }
}
