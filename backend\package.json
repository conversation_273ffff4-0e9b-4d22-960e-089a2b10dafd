{"name": "marketplace-backend", "version": "1.0.0", "description": "Backend automation for Facebook Marketplace posting", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["facebook", "marketplace", "automation", "playwright", "nodejs"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "better-sqlite3": "^11.10.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "playwright": "^1.40.0", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "puppeteer-extra-plugin-stealth": "^2.11.2", "sqlite3": "^5.1.7", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}