const express = require('express');
const router = express.Router();
const CryptoJS = require('crypto-js');
const { accountQueries, logQueries } = require('../database');

// Encryption key (should be in environment variables)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-this';

// Helper functions
const encryptData = (data) => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
};

const decryptData = (encryptedData) => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
        throw new Error('Failed to decrypt data');
    }
};

// GET /api/accounts - Get all accounts
router.get('/', (req, res) => {
    try {
        const accounts = accountQueries.getAll.all();
        // Return accounts without passwords for security
        const safeAccounts = accounts.map(account => ({
            id: account.id,
            name: account.name,
            email: account.email,
            status: account.status,
            facebook_status: account.facebook_status,
            last_tested: account.last_tested,
            created_at: account.created_at,
            updated_at: account.updated_at
        }));

        res.json(safeAccounts);
    } catch (error) {
        console.error('Error fetching accounts:', error);
        logQueries.create.run('error', 'Failed to fetch accounts', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to fetch accounts' });
    }
});

// POST /api/accounts - Add new account
router.post('/', (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Name, email, and password are required' });
        }

        // Encrypt password
        const encryptedPassword = encryptData(password);

        // Insert into database
        const result = accountQueries.create.run(name, email, encryptedPassword, 'inactive', 'unknown');

        // Get the created account
        const newAccount = accountQueries.getById.get(result.lastInsertRowid);

        // Return safe account data
        const safeAccount = {
            id: newAccount.id,
            name: newAccount.name,
            email: newAccount.email,
            status: newAccount.status,
            facebook_status: newAccount.facebook_status,
            last_tested: newAccount.last_tested,
            created_at: newAccount.created_at,
            updated_at: newAccount.updated_at
        };

        logQueries.create.run('info', `Account created: ${name}`, 'accounts', JSON.stringify(safeAccount));
        res.status(201).json(safeAccount);
    } catch (error) {
        console.error('Error creating account:', error);
        if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            return res.status(400).json({ error: 'Account with this email already exists' });
        }
        logQueries.create.run('error', 'Failed to create account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to create account' });
    }
});

// PUT /api/accounts/:id - Update account
router.put('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const { name, email, password, status, facebook_status } = req.body;

        // Check if account exists
        const existingAccount = accountQueries.getById.get(accountId);
        if (!existingAccount) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Prepare update data
        const updateName = name || existingAccount.name;
        const updateEmail = email || existingAccount.email;
        const updatePassword = password ? encryptData(password) : existingAccount.password;
        const updateStatus = status || existingAccount.status;
        const updateFacebookStatus = facebook_status || existingAccount.facebook_status;

        // Update in database
        accountQueries.update.run(updateName, updateEmail, updatePassword, updateStatus, updateFacebookStatus, accountId);

        // Get updated account
        const updatedAccount = accountQueries.getById.get(accountId);

        // Return safe account data
        const safeAccount = {
            id: updatedAccount.id,
            name: updatedAccount.name,
            email: updatedAccount.email,
            status: updatedAccount.status,
            facebook_status: updatedAccount.facebook_status,
            last_tested: updatedAccount.last_tested,
            created_at: updatedAccount.created_at,
            updated_at: updatedAccount.updated_at
        };

        logQueries.create.run('info', `Account updated: ${updatedAccount.name}`, 'accounts', JSON.stringify(safeAccount));
        res.json(safeAccount);
    } catch (error) {
        console.error('Error updating account:', error);
        logQueries.create.run('error', 'Failed to update account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to update account' });
    }
});

// DELETE /api/accounts/:id - Delete account
router.delete('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);

        // Check if account exists
        const existingAccount = accountQueries.getById.get(accountId);
        if (!existingAccount) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Delete from database
        accountQueries.delete.run(accountId);

        logQueries.create.run('info', `Account deleted: ${existingAccount.name}`, 'accounts', JSON.stringify({ id: accountId }));
        res.json({ message: 'Account deleted successfully' });
    } catch (error) {
        console.error('Error deleting account:', error);
        logQueries.create.run('error', 'Failed to delete account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to delete account' });
    }
});

// POST /api/accounts/:id/test - Test account login
router.post('/:id/test', async (req, res) => {
    const FacebookAutomation = require('../services/facebook-automation');

    try {
        const accountId = parseInt(req.params.id);
        const account = accountQueries.getById.get(accountId);

        if (!account) {
            return res.status(404).json({ error: 'Account not found' });
        }

        console.log(`🧪 Testing account: ${account.name} (${account.email})`);

        // فك تشفير كلمة المرور
        const CryptoJS = require('crypto-js');
        const secretKey = process.env.ENCRYPTION_KEY || 'default-secret-key';
        let decryptedPassword;

        try {
            const bytes = CryptoJS.AES.decrypt(account.password, secretKey);
            decryptedPassword = bytes.toString(CryptoJS.enc.Utf8);

            if (!decryptedPassword) {
                throw new Error('Failed to decrypt password');
            }
        } catch (decryptError) {
            console.error('❌ Password decryption failed:', decryptError);
            return res.status(400).json({
                success: false,
                error: 'Failed to decrypt account password',
                facebookStatus: 'error'
            });
        }

        // إنشاء instance من Facebook automation
        const fbAutomation = new FacebookAutomation();

        try {
            // تشغيل المتصفح
            const browserLaunched = await fbAutomation.launchBrowser();
            if (!browserLaunched) {
                throw new Error('Failed to launch browser');
            }

            // تحميل Facebook
            const facebookLoaded = await fbAutomation.loadFacebook();
            if (!facebookLoaded) {
                throw new Error('Failed to load Facebook');
            }

            // محاولة تسجيل الدخول
            const loginResult = await fbAutomation.login(account.email, decryptedPassword);

            if (loginResult.success) {
                // تحديث حالة الحساب إلى نشط
                accountQueries.updateStatus.run('active', 'connected', accountId);

                // تسجيل النجاح
                logQueries.create.run('info', `Account login test successful: ${account.name}`, 'accounts', JSON.stringify({
                    id: accountId,
                    email: account.email,
                    loginTime: new Date().toISOString()
                }));

                res.json({
                    success: true,
                    message: loginResult.message || 'Account login test successful',
                    facebookStatus: 'connected',
                    details: {
                        loginTime: new Date().toISOString(),
                        browser: 'Chromium with Stealth',
                        automation: 'Playwright'
                    }
                });
            } else {
                // تحديث حالة الحساب إلى خطأ
                accountQueries.updateStatus.run('inactive', 'error', accountId);

                // تسجيل الفشل
                logQueries.create.run('error', `Account login test failed: ${account.name} - ${loginResult.message}`, 'accounts', JSON.stringify({
                    id: accountId,
                    email: account.email,
                    error: loginResult.message,
                    requiresCaptcha: loginResult.requiresCaptcha || false
                }));

                res.json({
                    success: false,
                    message: loginResult.message || 'Login test failed',
                    facebookStatus: 'error',
                    requiresCaptcha: loginResult.requiresCaptcha || false,
                    details: {
                        testTime: new Date().toISOString(),
                        error: loginResult.message
                    }
                });
            }
        } finally {
            // إغلاق المتصفح
            await fbAutomation.close();
        }

    } catch (error) {
        console.error('❌ Error testing account:', error);

        // تحديث حالة الحساب إلى خطأ
        try {
            accountQueries.updateStatus.run('inactive', 'error', accountId);
        } catch (updateError) {
            console.error('Failed to update account status:', updateError);
        }

        // تسجيل الخطأ
        logQueries.create.run('error', `Failed to test account: ${error.message}`, 'accounts', JSON.stringify({
            accountId: accountId,
            error: error.message,
            stack: error.stack
        }));

        res.status(500).json({
            success: false,
            error: 'Failed to test account',
            message: error.message,
            facebookStatus: 'error'
        });
    }
});

module.exports = router;
