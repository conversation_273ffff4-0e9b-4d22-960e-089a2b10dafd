const express = require('express');
const router = express.Router();
const CryptoJS = require('crypto-js');
const { accountQueries, logQueries } = require('../database');

// Encryption key (should be in environment variables)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-this';

// Helper functions
const encryptData = (data) => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
};

const decryptData = (encryptedData) => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
        throw new Error('Failed to decrypt data');
    }
};

// GET /api/accounts - Get all accounts
router.get('/', (req, res) => {
    try {
        const accounts = accountQueries.getAll.all();
        // Return accounts without passwords for security
        const safeAccounts = accounts.map(account => ({
            id: account.id,
            name: account.name,
            email: account.email,
            status: account.status,
            facebook_status: account.facebook_status,
            last_tested: account.last_tested,
            created_at: account.created_at,
            updated_at: account.updated_at
        }));

        res.json(safeAccounts);
    } catch (error) {
        console.error('Error fetching accounts:', error);
        logQueries.create.run('error', 'Failed to fetch accounts', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to fetch accounts' });
    }
});

// POST /api/accounts - Add new account
router.post('/', (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Name, email, and password are required' });
        }

        // Encrypt password
        const encryptedPassword = encryptData(password);

        // Insert into database
        const result = accountQueries.create.run(name, email, encryptedPassword, 'inactive', 'unknown');

        // Get the created account
        const newAccount = accountQueries.getById.get(result.lastInsertRowid);

        // Return safe account data
        const safeAccount = {
            id: newAccount.id,
            name: newAccount.name,
            email: newAccount.email,
            status: newAccount.status,
            facebook_status: newAccount.facebook_status,
            last_tested: newAccount.last_tested,
            created_at: newAccount.created_at,
            updated_at: newAccount.updated_at
        };

        logQueries.create.run('info', `Account created: ${name}`, 'accounts', JSON.stringify(safeAccount));
        res.status(201).json(safeAccount);
    } catch (error) {
        console.error('Error creating account:', error);
        if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            return res.status(400).json({ error: 'Account with this email already exists' });
        }
        logQueries.create.run('error', 'Failed to create account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to create account' });
    }
});

// PUT /api/accounts/:id - Update account
router.put('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const { name, email, password, status, facebook_status } = req.body;

        // Check if account exists
        const existingAccount = accountQueries.getById.get(accountId);
        if (!existingAccount) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Prepare update data
        const updateName = name || existingAccount.name;
        const updateEmail = email || existingAccount.email;
        const updatePassword = password ? encryptData(password) : existingAccount.password;
        const updateStatus = status || existingAccount.status;
        const updateFacebookStatus = facebook_status || existingAccount.facebook_status;

        // Update in database
        accountQueries.update.run(updateName, updateEmail, updatePassword, updateStatus, updateFacebookStatus, accountId);

        // Get updated account
        const updatedAccount = accountQueries.getById.get(accountId);

        // Return safe account data
        const safeAccount = {
            id: updatedAccount.id,
            name: updatedAccount.name,
            email: updatedAccount.email,
            status: updatedAccount.status,
            facebook_status: updatedAccount.facebook_status,
            last_tested: updatedAccount.last_tested,
            created_at: updatedAccount.created_at,
            updated_at: updatedAccount.updated_at
        };

        logQueries.create.run('info', `Account updated: ${updatedAccount.name}`, 'accounts', JSON.stringify(safeAccount));
        res.json(safeAccount);
    } catch (error) {
        console.error('Error updating account:', error);
        logQueries.create.run('error', 'Failed to update account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to update account' });
    }
});

// DELETE /api/accounts/:id - Delete account
router.delete('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);

        // Check if account exists
        const existingAccount = accountQueries.getById.get(accountId);
        if (!existingAccount) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Delete from database
        accountQueries.delete.run(accountId);

        logQueries.create.run('info', `Account deleted: ${existingAccount.name}`, 'accounts', JSON.stringify({ id: accountId }));
        res.json({ message: 'Account deleted successfully' });
    } catch (error) {
        console.error('Error deleting account:', error);
        logQueries.create.run('error', 'Failed to delete account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to delete account' });
    }
});

// POST /api/accounts/:id/test - Test account login
router.post('/:id/test', async (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const account = accountQueries.getById.get(accountId);

        if (!account) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // TODO: Implement actual login test with Playwright
        // For now, simulate a test
        setTimeout(() => {
            // Update account status
            accountQueries.updateStatus.run('active', 'connected', accountId);

            logQueries.create.run('info', `Account test successful: ${account.name}`, 'accounts', JSON.stringify({ id: accountId }));
            res.json({
                success: true,
                message: 'Account login test successful',
                facebookStatus: 'connected',
                details: {}
            });
        }, 2000);

    } catch (error) {
        console.error('Error testing account:', error);
        logQueries.create.run('error', 'Failed to test account', 'accounts', JSON.stringify(error));
        res.status(500).json({ error: 'Failed to test account' });
    }
});

module.exports = router;
