const express = require('express');
const router = express.Router();
const CryptoJS = require('crypto-js');

// In-memory storage for demo (replace with database)
let accounts = [];
let accountIdCounter = 1;

// Encryption key (should be in environment variables)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-this';

// Helper functions
const encryptData = (data) => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
};

const decryptData = (encryptedData) => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
        throw new Error('Failed to decrypt data');
    }
};

// GET /api/accounts - Get all accounts
router.get('/', (req, res) => {
    try {
        // Return accounts without sensitive data
        const safeAccounts = accounts.map(account => ({
            id: account.id,
            name: account.name,
            email: account.email,
            status: account.status,
            proxyId: account.proxyId,
            lastLogin: account.lastLogin,
            createdAt: account.createdAt
        }));
        
        res.json(safeAccounts);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/accounts - Add new account
router.post('/', (req, res) => {
    try {
        const { name, email, password, proxyId } = req.body;
        
        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Name, email, and password are required' });
        }
        
        // Check if email already exists
        const existingAccount = accounts.find(acc => acc.email === email);
        if (existingAccount) {
            return res.status(400).json({ error: 'Account with this email already exists' });
        }
        
        // Encrypt sensitive data
        const encryptedCredentials = encryptData({ email, password });
        
        const newAccount = {
            id: accountIdCounter++,
            name,
            email,
            encryptedCredentials,
            status: 'inactive',
            proxyId: proxyId || null,
            lastLogin: null,
            createdAt: new Date().toISOString()
        };
        
        accounts.push(newAccount);
        
        // Return safe account data
        const safeAccount = {
            id: newAccount.id,
            name: newAccount.name,
            email: newAccount.email,
            status: newAccount.status,
            proxyId: newAccount.proxyId,
            lastLogin: newAccount.lastLogin,
            createdAt: newAccount.createdAt
        };
        
        res.status(201).json(safeAccount);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// PUT /api/accounts/:id - Update account
router.put('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const { name, email, password, proxyId, status } = req.body;
        
        const accountIndex = accounts.findIndex(acc => acc.id === accountId);
        if (accountIndex === -1) {
            return res.status(404).json({ error: 'Account not found' });
        }
        
        const account = accounts[accountIndex];
        
        // Update fields
        if (name) account.name = name;
        if (email) account.email = email;
        if (password) {
            // Re-encrypt with new password
            const decryptedData = decryptData(account.encryptedCredentials);
            decryptedData.password = password;
            account.encryptedCredentials = encryptData(decryptedData);
        }
        if (proxyId !== undefined) account.proxyId = proxyId;
        if (status) account.status = status;
        
        // Return safe account data
        const safeAccount = {
            id: account.id,
            name: account.name,
            email: account.email,
            status: account.status,
            proxyId: account.proxyId,
            lastLogin: account.lastLogin,
            createdAt: account.createdAt
        };
        
        res.json(safeAccount);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// DELETE /api/accounts/:id - Delete account
router.delete('/:id', (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const accountIndex = accounts.findIndex(acc => acc.id === accountId);
        
        if (accountIndex === -1) {
            return res.status(404).json({ error: 'Account not found' });
        }
        
        accounts.splice(accountIndex, 1);
        res.json({ message: 'Account deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/accounts/:id/test - Test account login
router.post('/:id/test', async (req, res) => {
    try {
        const accountId = parseInt(req.params.id);
        const account = accounts.find(acc => acc.id === accountId);
        
        if (!account) {
            return res.status(404).json({ error: 'Account not found' });
        }
        
        // TODO: Implement actual login test with Playwright
        // For now, simulate a test
        setTimeout(() => {
            account.status = 'active';
            account.lastLogin = new Date().toISOString();
            res.json({ success: true, message: 'Account login test successful' });
        }, 2000);
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
