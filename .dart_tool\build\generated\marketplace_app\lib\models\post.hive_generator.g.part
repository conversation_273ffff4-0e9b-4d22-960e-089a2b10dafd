// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PostAdapter extends TypeAdapter<Post> {
  @override
  final int typeId = 2;

  @override
  Post read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Post(
      id: fields[0] as int?,
      title: fields[1] as String,
      description: fields[2] as String,
      price: fields[3] as double,
      category: fields[4] as String,
      location: fields[5] as String,
      images: (fields[6] as List?)?.cast<PostImage>(),
      accountIds: (fields[7] as List?)?.cast<int>(),
      status: fields[8] as PostStatus,
      createdAt: fields[9] as DateTime?,
      scheduledAt: fields[10] as DateTime?,
      publishedAt: fields[11] as DateTime?,
      isTemplate: fields[12] as bool,
      templateName: fields[13] as String?,
      metadata: (fields[14] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Post obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.category)
      ..writeByte(5)
      ..write(obj.location)
      ..writeByte(6)
      ..write(obj.images)
      ..writeByte(7)
      ..write(obj.accountIds)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.scheduledAt)
      ..writeByte(11)
      ..write(obj.publishedAt)
      ..writeByte(12)
      ..write(obj.isTemplate)
      ..writeByte(13)
      ..write(obj.templateName)
      ..writeByte(14)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PostImageAdapter extends TypeAdapter<PostImage> {
  @override
  final int typeId = 3;

  @override
  PostImage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PostImage(
      filename: fields[0] as String,
      originalName: fields[1] as String,
      path: fields[2] as String,
      size: fields[3] as int,
      isUrl: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PostImage obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.filename)
      ..writeByte(1)
      ..write(obj.originalName)
      ..writeByte(2)
      ..write(obj.path)
      ..writeByte(3)
      ..write(obj.size)
      ..writeByte(4)
      ..write(obj.isUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostImageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PostStatusAdapter extends TypeAdapter<PostStatus> {
  @override
  final int typeId = 4;

  @override
  PostStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PostStatus.draft;
      case 1:
        return PostStatus.scheduled;
      case 2:
        return PostStatus.publishing;
      case 3:
        return PostStatus.published;
      case 4:
        return PostStatus.failed;
      case 5:
        return PostStatus.paused;
      default:
        return PostStatus.draft;
    }
  }

  @override
  void write(BinaryWriter writer, PostStatus obj) {
    switch (obj) {
      case PostStatus.draft:
        writer.writeByte(0);
        break;
      case PostStatus.scheduled:
        writer.writeByte(1);
        break;
      case PostStatus.publishing:
        writer.writeByte(2);
        break;
      case PostStatus.published:
        writer.writeByte(3);
        break;
      case PostStatus.failed:
        writer.writeByte(4);
        break;
      case PostStatus.paused:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
