const express = require('express');
const router = express.Router();
const axios = require('axios');
const CryptoJS = require('crypto-js');

// In-memory storage for demo
let proxies = [];
let proxyIdCounter = 1;

// Encryption key
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-this';

// Helper functions
const encryptData = (data) => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
};

const decryptData = (encryptedData) => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
        throw new Error('Failed to decrypt data');
    }
};

// Test proxy connection
const testProxy = async (proxyConfig) => {
    try {
        const { host, port, username, password, type } = proxyConfig;
        
        // Configure axios with proxy
        const axiosConfig = {
            timeout: 10000,
            proxy: {
                host,
                port: parseInt(port),
                protocol: type.toLowerCase()
            }
        };
        
        if (username && password) {
            axiosConfig.proxy.auth = { username, password };
        }
        
        // Test with a simple HTTP request
        const response = await axios.get('http://httpbin.org/ip', axiosConfig);
        
        return {
            success: true,
            ip: response.data.origin,
            responseTime: response.headers['x-response-time'] || 'N/A'
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
};

// GET /api/proxies - Get all proxies
router.get('/', (req, res) => {
    try {
        // Return proxies without sensitive data
        const safeProxies = proxies.map(proxy => ({
            id: proxy.id,
            name: proxy.name,
            host: proxy.host,
            port: proxy.port,
            type: proxy.type,
            status: proxy.status,
            lastTested: proxy.lastTested,
            responseTime: proxy.responseTime,
            createdAt: proxy.createdAt
        }));
        
        res.json(safeProxies);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/proxies - Add new proxy
router.post('/', (req, res) => {
    try {
        const { name, host, port, type, username, password } = req.body;
        
        if (!name || !host || !port || !type) {
            return res.status(400).json({ error: 'Name, host, port, and type are required' });
        }
        
        // Validate proxy type
        const validTypes = ['HTTP', 'HTTPS', 'SOCKS4', 'SOCKS5'];
        if (!validTypes.includes(type.toUpperCase())) {
            return res.status(400).json({ error: 'Invalid proxy type' });
        }
        
        // Encrypt credentials if provided
        let encryptedCredentials = null;
        if (username && password) {
            encryptedCredentials = encryptData({ username, password });
        }
        
        const newProxy = {
            id: proxyIdCounter++,
            name,
            host,
            port: parseInt(port),
            type: type.toUpperCase(),
            encryptedCredentials,
            status: 'untested',
            lastTested: null,
            responseTime: null,
            createdAt: new Date().toISOString()
        };
        
        proxies.push(newProxy);
        
        // Return safe proxy data
        const safeProxy = {
            id: newProxy.id,
            name: newProxy.name,
            host: newProxy.host,
            port: newProxy.port,
            type: newProxy.type,
            status: newProxy.status,
            lastTested: newProxy.lastTested,
            responseTime: newProxy.responseTime,
            createdAt: newProxy.createdAt
        };
        
        res.status(201).json(safeProxy);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// PUT /api/proxies/:id - Update proxy
router.put('/:id', (req, res) => {
    try {
        const proxyId = parseInt(req.params.id);
        const { name, host, port, type, username, password } = req.body;
        
        const proxyIndex = proxies.findIndex(proxy => proxy.id === proxyId);
        if (proxyIndex === -1) {
            return res.status(404).json({ error: 'Proxy not found' });
        }
        
        const proxy = proxies[proxyIndex];
        
        // Update fields
        if (name) proxy.name = name;
        if (host) proxy.host = host;
        if (port) proxy.port = parseInt(port);
        if (type) {
            const validTypes = ['HTTP', 'HTTPS', 'SOCKS4', 'SOCKS5'];
            if (!validTypes.includes(type.toUpperCase())) {
                return res.status(400).json({ error: 'Invalid proxy type' });
            }
            proxy.type = type.toUpperCase();
        }
        
        // Update credentials
        if (username !== undefined || password !== undefined) {
            if (username && password) {
                proxy.encryptedCredentials = encryptData({ username, password });
            } else {
                proxy.encryptedCredentials = null;
            }
        }
        
        // Reset status when proxy is updated
        proxy.status = 'untested';
        proxy.lastTested = null;
        proxy.responseTime = null;
        
        // Return safe proxy data
        const safeProxy = {
            id: proxy.id,
            name: proxy.name,
            host: proxy.host,
            port: proxy.port,
            type: proxy.type,
            status: proxy.status,
            lastTested: proxy.lastTested,
            responseTime: proxy.responseTime,
            createdAt: proxy.createdAt
        };
        
        res.json(safeProxy);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// DELETE /api/proxies/:id - Delete proxy
router.delete('/:id', (req, res) => {
    try {
        const proxyId = parseInt(req.params.id);
        const proxyIndex = proxies.findIndex(proxy => proxy.id === proxyId);
        
        if (proxyIndex === -1) {
            return res.status(404).json({ error: 'Proxy not found' });
        }
        
        proxies.splice(proxyIndex, 1);
        res.json({ message: 'Proxy deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/proxies/:id/test - Test proxy connection
router.post('/:id/test', async (req, res) => {
    try {
        const proxyId = parseInt(req.params.id);
        const proxy = proxies.find(p => p.id === proxyId);
        
        if (!proxy) {
            return res.status(404).json({ error: 'Proxy not found' });
        }
        
        // Prepare proxy config for testing
        const proxyConfig = {
            host: proxy.host,
            port: proxy.port,
            type: proxy.type
        };
        
        // Decrypt credentials if available
        if (proxy.encryptedCredentials) {
            const credentials = decryptData(proxy.encryptedCredentials);
            proxyConfig.username = credentials.username;
            proxyConfig.password = credentials.password;
        }
        
        // Test the proxy
        const testResult = await testProxy(proxyConfig);
        
        // Update proxy status
        proxy.lastTested = new Date().toISOString();
        if (testResult.success) {
            proxy.status = 'active';
            proxy.responseTime = testResult.responseTime;
        } else {
            proxy.status = 'failed';
            proxy.responseTime = null;
        }
        
        res.json({
            success: testResult.success,
            message: testResult.success ? 'Proxy test successful' : 'Proxy test failed',
            error: testResult.error,
            ip: testResult.ip,
            responseTime: testResult.responseTime
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/proxies/test-all - Test all proxies
router.post('/test-all', async (req, res) => {
    try {
        const results = [];
        
        for (const proxy of proxies) {
            const proxyConfig = {
                host: proxy.host,
                port: proxy.port,
                type: proxy.type
            };
            
            if (proxy.encryptedCredentials) {
                const credentials = decryptData(proxy.encryptedCredentials);
                proxyConfig.username = credentials.username;
                proxyConfig.password = credentials.password;
            }
            
            const testResult = await testProxy(proxyConfig);
            
            proxy.lastTested = new Date().toISOString();
            if (testResult.success) {
                proxy.status = 'active';
                proxy.responseTime = testResult.responseTime;
            } else {
                proxy.status = 'failed';
                proxy.responseTime = null;
            }
            
            results.push({
                id: proxy.id,
                name: proxy.name,
                success: testResult.success,
                error: testResult.error,
                ip: testResult.ip,
                responseTime: testResult.responseTime
            });
        }
        
        res.json({ results });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
