const express = require('express');
const router = express.Router();
const winston = require('winston');
const path = require('path');

// Configure Winston logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'marketplace-backend' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.simple()
        })
    ]
});

// In-memory storage for demo (in production, use database)
let logs = [];
let logIdCounter = 1;

// Log levels
const LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
};

// Log categories
const LOG_CATEGORIES = {
    ACCOUNT: 'account',
    POST: 'post',
    PROXY: 'proxy',
    SCHEDULER: 'scheduler',
    SYSTEM: 'system',
    AUTOMATION: 'automation'
};

// Helper function to add log entry
function addLogEntry(level, category, message, details = null, accountId = null, postId = null) {
    const logEntry = {
        id: logIdCounter++,
        timestamp: new Date().toISOString(),
        level,
        category,
        message,
        details,
        accountId,
        postId
    };
    
    logs.unshift(logEntry); // Add to beginning for newest first
    
    // Keep only last 1000 logs in memory
    if (logs.length > 1000) {
        logs = logs.slice(0, 1000);
    }
    
    // Log to Winston
    logger.log(level, message, { category, details, accountId, postId });
    
    return logEntry;
}

// GET /api/logs - Get logs with filtering and pagination
router.get('/', (req, res) => {
    try {
        const {
            level,
            category,
            accountId,
            postId,
            startDate,
            endDate,
            page = 1,
            limit = 50,
            search
        } = req.query;
        
        let filteredLogs = [...logs];
        
        // Apply filters
        if (level) {
            filteredLogs = filteredLogs.filter(log => log.level === level);
        }
        
        if (category) {
            filteredLogs = filteredLogs.filter(log => log.category === category);
        }
        
        if (accountId) {
            filteredLogs = filteredLogs.filter(log => log.accountId === parseInt(accountId));
        }
        
        if (postId) {
            filteredLogs = filteredLogs.filter(log => log.postId === parseInt(postId));
        }
        
        if (startDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(startDate));
        }
        
        if (endDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(endDate));
        }
        
        if (search) {
            const searchLower = search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
                log.message.toLowerCase().includes(searchLower) ||
                (log.details && JSON.stringify(log.details).toLowerCase().includes(searchLower))
            );
        }
        
        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
        
        res.json({
            logs: paginatedLogs,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(filteredLogs.length / limit),
                totalLogs: filteredLogs.length,
                hasNext: endIndex < filteredLogs.length,
                hasPrev: startIndex > 0
            }
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// POST /api/logs - Add new log entry
router.post('/', (req, res) => {
    try {
        const { level, category, message, details, accountId, postId } = req.body;
        
        if (!level || !category || !message) {
            return res.status(400).json({ error: 'Level, category, and message are required' });
        }
        
        if (!Object.values(LOG_LEVELS).includes(level)) {
            return res.status(400).json({ error: 'Invalid log level' });
        }
        
        if (!Object.values(LOG_CATEGORIES).includes(category)) {
            return res.status(400).json({ error: 'Invalid log category' });
        }
        
        const logEntry = addLogEntry(level, category, message, details, accountId, postId);
        res.status(201).json(logEntry);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// DELETE /api/logs - Clear logs
router.delete('/', (req, res) => {
    try {
        const { level, category, olderThan } = req.query;
        
        if (olderThan) {
            const cutoffDate = new Date(olderThan);
            logs = logs.filter(log => new Date(log.timestamp) > cutoffDate);
        } else if (level) {
            logs = logs.filter(log => log.level !== level);
        } else if (category) {
            logs = logs.filter(log => log.category !== category);
        } else {
            // Clear all logs
            logs = [];
        }
        
        res.json({ message: 'Logs cleared successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// GET /api/logs/stats - Get log statistics
router.get('/stats', (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        let filteredLogs = [...logs];
        
        if (startDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(startDate));
        }
        
        if (endDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(endDate));
        }
        
        // Count by level
        const levelStats = {};
        Object.values(LOG_LEVELS).forEach(level => {
            levelStats[level] = filteredLogs.filter(log => log.level === level).length;
        });
        
        // Count by category
        const categoryStats = {};
        Object.values(LOG_CATEGORIES).forEach(category => {
            categoryStats[category] = filteredLogs.filter(log => log.category === category).length;
        });
        
        // Recent activity (last 24 hours)
        const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentLogs = filteredLogs.filter(log => new Date(log.timestamp) > last24Hours);
        
        res.json({
            total: filteredLogs.length,
            levelStats,
            categoryStats,
            recentActivity: recentLogs.length,
            dateRange: {
                oldest: filteredLogs.length > 0 ? filteredLogs[filteredLogs.length - 1].timestamp : null,
                newest: filteredLogs.length > 0 ? filteredLogs[0].timestamp : null
            }
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// GET /api/logs/export - Export logs
router.get('/export', (req, res) => {
    try {
        const { format = 'json', level, category, startDate, endDate } = req.query;
        
        let filteredLogs = [...logs];
        
        // Apply filters
        if (level) {
            filteredLogs = filteredLogs.filter(log => log.level === level);
        }
        
        if (category) {
            filteredLogs = filteredLogs.filter(log => log.category === category);
        }
        
        if (startDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(startDate));
        }
        
        if (endDate) {
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(endDate));
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        if (format === 'csv') {
            // Convert to CSV
            const csvHeader = 'ID,Timestamp,Level,Category,Message,AccountID,PostID,Details\n';
            const csvRows = filteredLogs.map(log => {
                const details = log.details ? JSON.stringify(log.details).replace(/"/g, '""') : '';
                return `${log.id},"${log.timestamp}","${log.level}","${log.category}","${log.message.replace(/"/g, '""')}",${log.accountId || ''},${log.postId || ''},"${details}"`;
            }).join('\n');
            
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="logs-${timestamp}.csv"`);
            res.send(csvHeader + csvRows);
        } else {
            // Default to JSON
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="logs-${timestamp}.json"`);
            res.json(filteredLogs);
        }
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Middleware to automatically log API requests
router.use((req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
        // Log API request
        addLogEntry(
            LOG_LEVELS.INFO,
            LOG_CATEGORIES.SYSTEM,
            `API Request: ${req.method} ${req.originalUrl}`,
            {
                method: req.method,
                url: req.originalUrl,
                statusCode: res.statusCode,
                userAgent: req.get('User-Agent')
            }
        );
        
        originalSend.call(this, data);
    };
    
    next();
});

// Export the addLogEntry function for use in other modules
module.exports = router;
module.exports.addLogEntry = addLogEntry;
module.exports.LOG_LEVELS = LOG_LEVELS;
module.exports.LOG_CATEGORIES = LOG_CATEGORIES;
