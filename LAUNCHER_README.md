# 🚀 Facebook Marketplace Automation - Professional Launcher

## 📋 **نظرة عامة**

هذه مجموعة من الأدوات الاحترافية لتشغيل وإدارة نظام Facebook Marketplace Automation بسهولة ومرونة.

## 🎯 **الملفات المتاحة**

### **🚀 الملفات الرئيسية:**

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `🚀 Start Marketplace App.bat` | **تشغيل النظام الكامل** | نقرة مزدوجة لتشغيل Backend + Frontend |
| `🔧 Start Backend Only.bat` | **تشغيل السيرفر فقط** | لتشغيل Backend Server فقط |
| `🎨 Start Frontend Only.bat` | **تشغيل الواجهة فقط** | لتشغيل Flutter Frontend فقط |
| `🛑 Stop All Services.bat` | **إيقاف جميع الخدمات** | لإيقاف جميع العمليات |
| `📊 Check Status.bat` | **فحص الحالة** | لفحص حالة الخدمات |

### **⚙️ الملفات التقنية:**
- `start-marketplace-app.ps1` - السكريبت الرئيسي (PowerShell)
- `launcher.log` - ملف السجلات (يتم إنشاؤه تلقائياً)

## 🎮 **كيفية الاستخدام**

### **🚀 التشغيل السريع:**
1. **انقر نقرة مزدوجة** على `🚀 Start Marketplace App.bat`
2. **انتظر** حتى يتم تشغيل النظام
3. **ستفتح نوافذ جديدة** للـ Backend والـ Frontend

### **🔧 تشغيل مخصص:**

#### **Backend فقط:**
```bash
# انقر على:
🔧 Start Backend Only.bat

# أو استخدم PowerShell:
.\start-marketplace-app.ps1 -Backend
```

#### **Frontend فقط:**
```bash
# انقر على:
🎨 Start Frontend Only.bat

# أو استخدم PowerShell:
.\start-marketplace-app.ps1 -Frontend
```

#### **كلاهما:**
```bash
# انقر على:
🚀 Start Marketplace App.bat

# أو استخدم PowerShell:
.\start-marketplace-app.ps1 -Both
```

### **🛑 إيقاف الخدمات:**
```bash
# انقر على:
🛑 Stop All Services.bat

# أو استخدم PowerShell:
.\start-marketplace-app.ps1 -Stop
```

### **📊 فحص الحالة:**
```bash
# انقر على:
📊 Check Status.bat

# أو استخدم PowerShell:
.\start-marketplace-app.ps1 -Status
```

## 🔍 **الميزات المتقدمة**

### **✅ فحص المتطلبات التلقائي:**
- فحص وجود Node.js
- فحص وجود Flutter
- فحص وجود ملفات المشروع
- تقرير مفصل عن المتطلبات المفقودة

### **🔄 إدارة العمليات الذكية:**
- كشف العمليات المتشغلة على البورتات
- إيقاف العمليات القديمة تلقائياً
- انتظار تشغيل الخدمات بالكامل
- تأكيد نجاح التشغيل

### **📝 نظام السجلات:**
- تسجيل جميع العمليات في `launcher.log`
- طوابع زمنية دقيقة
- مستويات مختلفة للرسائل (INFO, SUCCESS, ERROR)
- عرض آخر 20 سجل

### **🎨 واجهة احترافية:**
- ألوان مميزة لكل نوع من الرسائل
- رسوم ASCII فنية
- رسائل واضحة ومفصلة
- تقارير حالة شاملة

## 🛠️ **استكشاف الأخطاء**

### **❌ مشاكل شائعة:**

#### **"PowerShell script not found":**
```bash
# تأكد من وجود الملف:
start-marketplace-app.ps1
```

#### **"Port already in use":**
```bash
# استخدم إيقاف الخدمات:
🛑 Stop All Services.bat
```

#### **"Node.js not found":**
```bash
# تثبيت Node.js من:
https://nodejs.org/
```

#### **"Flutter not found":**
```bash
# تثبيت Flutter من:
https://flutter.dev/docs/get-started/install
```

### **🔧 حلول متقدمة:**

#### **تشغيل PowerShell يدوياً:**
```powershell
# فتح PowerShell كمدير
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# تشغيل السكريبت
.\start-marketplace-app.ps1 -Both
```

#### **فحص البورتات:**
```bash
# فحص البورت 3000:
netstat -ano | findstr :3000
```

## 📊 **مراقبة النظام**

### **🟢 علامات النجاح:**
- `✅ Backend Server: http://localhost:3000`
- `✅ Flutter Frontend: Started`
- `✅ All prerequisites met!`

### **🔴 علامات المشاكل:**
- `❌ Failed to start backend server`
- `❌ Missing prerequisites`
- `🔴 Backend Server: Not running`

### **⚠️ تحذيرات:**
- `⚠️ Port 3000 is already in use`
- `⚠️ Page still loading, but login appears successful`

## 🎯 **نصائح للاستخدام الأمثل**

1. **استخدم الملفات .bat** للتشغيل السريع
2. **راقب نوافذ PowerShell** للتفاصيل التقنية
3. **استخدم فحص الحالة** للتأكد من عمل النظام
4. **أوقف الخدمات** قبل إغلاق الكمبيوتر
5. **راجع ملف السجلات** عند حدوث مشاكل

## 🔗 **روابط مفيدة**

- **Backend Server:** http://localhost:3000
- **Flutter Frontend:** يفتح تلقائياً في المتصفح
- **ملف السجلات:** `launcher.log`
- **دليل الاستخدام:** هذا الملف

---

## 🎉 **استمتع بالاستخدام!**

هذه الأدوات مصممة لتسهيل عملك وتوفير الوقت. في حالة وجود أي مشاكل أو اقتراحات، يرجى مراجعة ملف السجلات أو التواصل مع فريق التطوير.

**Happy Coding! 🚀**
