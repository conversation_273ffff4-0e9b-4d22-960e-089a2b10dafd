{"version": 3, "file": "index.esm.js", "sources": ["../src/content.ts", "../src/content-hcaptcha.ts", "../src/provider/2captcha-api.ts", "../src/provider/2captcha.ts", "../src/index.ts"], "sourcesContent": ["import * as types from './types'\n\nexport const ContentScriptDefaultOpts: types.ContentScriptOpts = {\n  visualFeedback: true,\n  debugBinding: undefined\n}\n\nexport const ContentScriptDefaultData: types.ContentScriptData = {\n  solutions: []\n}\n\ninterface FrameSources {\n  anchor: string[]\n  bframe: string[]\n}\n\n/**\n * Content script for Recaptcha handling (runs in browser context)\n * @note External modules are not supported here (due to content script isolation)\n */\nexport class RecaptchaContentScript {\n  private opts: types.ContentScriptOpts\n  private data: types.ContentScriptData\n  private frameSources: FrameSources\n\n  constructor(\n    opts = ContentScriptDefaultOpts,\n    data = ContentScriptDefaultData\n  ) {\n    // Workaround for https://github.com/esbuild-kit/tsx/issues/113\n    if (typeof globalThis.__name === 'undefined') {\n      globalThis.__defProp = Object.defineProperty\n      globalThis.__name = (target, value) =>\n        globalThis.__defProp(target, 'name', { value, configurable: true })\n    }\n\n    this.opts = opts\n    this.data = data\n    this.frameSources = this._generateFrameSources()\n    this.log('Intialized', { url: document.location.href, opts: this.opts })\n  }\n\n  /** Log using debug binding if available */\n  private log = (message: string, data?: any) => {\n    if (this.opts.debugBinding && window.top[this.opts.debugBinding]) {\n      window.top[this.opts.debugBinding](message, JSON.stringify(data))\n    }\n  }\n\n  // Poor mans _.pluck\n  private _pick = (props: any[]) => (o: any) =>\n    props.reduce((a, e) => ({ ...a, [e]: o[e] }), {})\n\n  // make sure the element is visible - this is equivalent to jquery's is(':visible')\n  private _isVisible = (elem: any) =>\n    !!(\n      elem.offsetWidth ||\n      elem.offsetHeight ||\n      (typeof elem.getClientRects === 'function' &&\n        elem.getClientRects().length)\n    )\n\n  /** Check if an element is in the current viewport */\n  private _isInViewport(elem: any) {\n    const rect = elem.getBoundingClientRect()\n    return (\n      rect.top >= 0 &&\n      rect.left >= 0 &&\n      rect.bottom <=\n        (window.innerHeight ||\n          (document.documentElement.clientHeight &&\n            rect.right <=\n              (window.innerWidth || document.documentElement.clientWidth)))\n    )\n  }\n\n  // Recaptcha client is a nested, circular object with object keys that seem generated\n  // We flatten that object a couple of levels deep for easy access to certain keys we're interested in.\n  private _flattenObject(item: any, levels = 2, ignoreHTML = true) {\n    const isObject = (x: any) => x && typeof x === 'object'\n    const isHTML = (x: any) => x && x instanceof HTMLElement\n    let newObj = {} as any\n    for (let i = 0; i < levels; i++) {\n      item = Object.keys(newObj).length ? newObj : item\n      Object.keys(item).forEach(key => {\n        if (ignoreHTML && isHTML(item[key])) return\n        if (isObject(item[key])) {\n          Object.keys(item[key]).forEach(innerKey => {\n            if (ignoreHTML && isHTML(item[key][innerKey])) return\n            const keyName = isObject(item[key][innerKey])\n              ? `obj_${key}_${innerKey}`\n              : `${innerKey}`\n            newObj[keyName] = item[key][innerKey]\n          })\n        } else {\n          newObj[key] = item[key]\n        }\n      })\n    }\n    return newObj\n  }\n\n  // Helper function to return an object based on a well known value\n  private _getKeyByValue(object: any, value: any) {\n    return Object.keys(object).find(key => object[key] === value)\n  }\n\n  private async _waitUntilDocumentReady() {\n    return new Promise(function(resolve) {\n      if (!document || !window) {\n        return resolve(null)\n      }\n      const loadedAlready = /^loaded|^i|^c/.test(document.readyState)\n      if (loadedAlready) {\n        return resolve(null)\n      }\n\n      function onReady() {\n        resolve(null)\n        document.removeEventListener('DOMContentLoaded', onReady)\n        window.removeEventListener('load', onReady)\n      }\n\n      document.addEventListener('DOMContentLoaded', onReady)\n      window.addEventListener('load', onReady)\n    })\n  }\n\n  private _paintCaptchaBusy($iframe: HTMLIFrameElement) {\n    try {\n      if (this.opts.visualFeedback) {\n        $iframe.style.filter = `opacity(60%) hue-rotate(400deg)` // violet\n      }\n    } catch (error) {\n      // noop\n    }\n    return $iframe\n  }\n\n  private _paintCaptchaSolved($iframe: HTMLIFrameElement) {\n    try {\n      if (this.opts.visualFeedback) {\n        $iframe.style.filter = `opacity(60%) hue-rotate(230deg)` // green\n      }\n    } catch (error) {\n      // noop\n    }\n    return $iframe\n  }\n\n  private _findVisibleIframeNodes() {\n    return Array.from(\n      document.querySelectorAll<HTMLIFrameElement>(\n        this.getFrameSelectorForId('anchor', '') // intentionally blank\n      )\n    )\n  }\n  private _findVisibleIframeNodeById(id?: string) {\n    return document.querySelector<HTMLIFrameElement>(\n      this.getFrameSelectorForId('anchor', id)\n    )\n  }\n\n  private _hideChallengeWindowIfPresent(id: string = '') {\n    let frame: HTMLElement | null = document.querySelector<HTMLIFrameElement>(\n      this.getFrameSelectorForId('bframe', id)\n    )\n    this.log(' - _hideChallengeWindowIfPresent', { id, hasFrame: !!frame })\n    if (!frame) {\n      return\n    }\n    while (\n      frame &&\n      frame.parentElement &&\n      frame.parentElement !== document.body\n    ) {\n      frame = frame.parentElement\n    }\n    if (frame) {\n      frame.style.visibility = 'hidden'\n    }\n  }\n\n  // There's so many different possible deployments URLs that we better generate them\n  private _generateFrameSources(): FrameSources {\n    const protos = ['http', 'https']\n    const hosts = [\n      'google.com',\n      'www.google.com',\n      'recaptcha.net',\n      'www.recaptcha.net'\n    ]\n    const origins = protos.flatMap(proto =>\n      hosts.map(host => `${proto}://${host}`)\n    )\n    const paths = {\n      anchor: ['/recaptcha/api2/anchor', '/recaptcha/enterprise/anchor'],\n      bframe: ['/recaptcha/api2/bframe', '/recaptcha/enterprise/bframe']\n    }\n    return {\n      anchor: origins.flatMap(origin =>\n        paths.anchor.map(path => `${origin}${path}`)\n      ),\n      bframe: origins.flatMap(origin =>\n        paths.bframe.map(path => `${origin}${path}`)\n      )\n    }\n  }\n\n  private getFrameSelectorForId(type: 'anchor' | 'bframe' = 'anchor', id = '') {\n    const namePrefix = type === 'anchor' ? 'a' : 'c'\n    return this.frameSources[type]\n      .map(src => `iframe[src^='${src}'][name^=\"${namePrefix}-${id}\"]`)\n      .join(',')\n  }\n\n  private getClients() {\n    // Bail out early if there's no indication of recaptchas\n    if (!window || !window.__google_recaptcha_client) return\n    if (!window.___grecaptcha_cfg || !window.___grecaptcha_cfg.clients) {\n      return\n    }\n    if (!Object.keys(window.___grecaptcha_cfg.clients).length) return\n    return window.___grecaptcha_cfg.clients\n  }\n\n  private getVisibleIframesIds() {\n    // Find all regular visible recaptcha boxes through their iframes\n    const result = this._findVisibleIframeNodes()\n      .filter($f => this._isVisible($f))\n      .map($f => this._paintCaptchaBusy($f))\n      .filter($f => $f && $f.getAttribute('name'))\n      .map($f => $f.getAttribute('name') || '') // a-841543e13666\n      .map(\n        rawId => rawId.split('-').slice(-1)[0] // a-841543e13666 => 841543e13666\n      )\n      .filter(id => id)\n    this.log('getVisibleIframesIds', result)\n    return result\n  }\n\n  // TODO: Obsolete with recent changes\n  private getInvisibleIframesIds() {\n    // Find all invisible recaptcha boxes through their iframes (only the ones with an active challenge window)\n    const result = this._findVisibleIframeNodes()\n      .filter($f => $f && $f.getAttribute('name'))\n      .map($f => $f.getAttribute('name') || '') // a-841543e13666\n      .map(\n        rawId => rawId.split('-').slice(-1)[0] // a-841543e13666 => 841543e13666\n      )\n      .filter(id => id)\n      .filter(\n        id =>\n          document.querySelectorAll(this.getFrameSelectorForId('bframe', id))\n            .length\n      )\n    this.log('getInvisibleIframesIds', result)\n    return result\n  }\n\n  private getIframesIds() {\n    // Find all recaptcha boxes through their iframes, check for invisible ones as fallback\n    const results = [\n      ...this.getVisibleIframesIds(),\n      ...this.getInvisibleIframesIds()\n    ]\n    this.log('getIframesIds', results)\n    // Deduplicate results by using the unique id as key\n    const dedup = Array.from(new Set(results))\n    this.log('getIframesIds - dedup', dedup)\n    return dedup\n  }\n\n  private isEnterpriseCaptcha(id?: string) {\n    if (!id) return false\n    // The only way to determine if a captcha is an enterprise one is by looking at their iframes\n    const prefix = 'iframe[src*=\"/recaptcha/\"][src*=\"/enterprise/\"]'\n    const nameSelectors = [`[name^=\"a-${id}\"]`, `[name^=\"c-${id}\"]`]\n    const fullSelector = nameSelectors.map(name => prefix + name).join(',')\n    return document.querySelectorAll(fullSelector).length > 0\n  }\n\n  private isInvisible(id?: string) {\n    if (!id) return false\n    const selector = `iframe[src*=\"/recaptcha/\"][src*=\"/anchor\"][name=\"a-${id}\"][src*=\"&size=invisible\"]`\n    return document.querySelectorAll(selector).length > 0\n  }\n\n  /** Whether an active challenge popup is open */\n  private hasActiveChallengePopup(id?: string) {\n    if (!id) return false\n    const selector = `iframe[src*=\"/recaptcha/\"][src*=\"/bframe\"][name=\"c-${id}\"]`\n    const elem = document.querySelector(selector)\n    if (!elem) {\n      return false\n    }\n    return this._isInViewport(elem) // note: _isVisible doesn't work here as the outer div is hidden, not the iframe itself\n  }\n\n  /** Whether an (invisible) captcha has a challenge bframe - otherwise it's a score based captcha */\n  private hasChallengeFrame(id?: string) {\n    if (!id) return false\n    return (\n      document.querySelectorAll(this.getFrameSelectorForId('bframe', id))\n        .length > 0\n    )\n  }\n\n  private isInViewport(id?: string) {\n    if (!id) return\n    const prefix = 'iframe[src*=\"recaptcha\"]'\n    const nameSelectors = [`[name^=\"a-${id}\"]`, `[name^=\"c-${id}\"]`]\n    const fullSelector = nameSelectors.map(name => prefix + name).join(',')\n    const elem = document.querySelector(fullSelector)\n    if (!elem) {\n      return false\n    }\n    return this._isInViewport(elem)\n  }\n\n  private getResponseInputById(id?: string) {\n    if (!id) return\n    const $iframe = this._findVisibleIframeNodeById(id)\n    if (!$iframe) return\n    const $parentForm = $iframe.closest(`form`)\n    if ($parentForm) {\n      return $parentForm.querySelector(`[name='g-recaptcha-response']`)\n    }\n    // Not all reCAPTCHAs are in forms\n    // https://github.com/berstend/puppeteer-extra/issues/57\n    if (document && document.body) {\n      return document.body.querySelector(`[name='g-recaptcha-response']`)\n    }\n  }\n\n  private getClientById(id?: string) {\n    if (!id) return\n    const clients = this.getClients()\n    // Lookup captcha \"client\" info using extracted id\n    let client: any = Object.values(clients || {})\n      .filter(obj => this._getKeyByValue(obj, id))\n      .shift() // returns first entry in array or undefined\n    this.log(' - getClientById:client', { id, hasClient: !!client })\n    if (!client) return\n    try {\n      client = this._flattenObject(client) as any\n      client.widgetId = client.id\n      client.id = id\n      this.log(' - getClientById:client:flatten', {\n        id,\n        hasClient: !!client\n      })\n    } catch (err) {\n      this.log(' - getClientById:client ERROR', err.toString())\n    }\n    return client\n  }\n\n  private extractInfoFromClient(client?: any) {\n    if (!client) return\n    const info: types.CaptchaInfo = this._pick(['sitekey', 'callback'])(client)\n    if (!info.sitekey) return\n    info._vendor = 'recaptcha'\n    info.id = client.id\n    info.s = client.s // google site specific\n    info.widgetId = client.widgetId\n    info.display = this._pick([\n      'size',\n      'top',\n      'left',\n      'width',\n      'height',\n      'theme'\n    ])(client)\n    if (client && client.action) {\n      info.action = client.action\n    }\n    // callbacks can be strings or funtion refs\n    if (info.callback && typeof info.callback === 'function') {\n      info.callback = info.callback.name || 'anonymous'\n    }\n    if (document && document.location) info.url = document.location.href\n    return info\n  }\n\n  public async findRecaptchas() {\n    const result = {\n      captchas: [] as (types.CaptchaInfo | undefined)[],\n      error: null as any\n    }\n    try {\n      await this._waitUntilDocumentReady()\n      const clients = this.getClients()\n      this.log('findRecaptchas', {\n        url: document.location.href,\n        hasClients: !!clients\n      })\n      if (!clients) return result\n      result.captchas = this.getIframesIds()\n        .map(id => this.getClientById(id))\n        .map(client => this.extractInfoFromClient(client))\n        .map(info => {\n          this.log(' - captchas:info', info)\n          if (!info) return\n          const $input = this.getResponseInputById(info.id)\n          info.hasResponseElement = !!$input\n          return info\n        })\n        .filter(info => !!info && !!info.sitekey)\n        .map(info => {\n          info.sitekey = info.sitekey.trim()\n          info.isEnterprise = this.isEnterpriseCaptcha(info.id)\n          info.isInViewport = this.isInViewport(info.id)\n          info.isInvisible = this.isInvisible(info.id)\n          info._type = 'checkbox'\n          if (info.isInvisible) {\n            info._type = 'invisible'\n            info.hasActiveChallengePopup = this.hasActiveChallengePopup(info.id)\n            info.hasChallengeFrame = this.hasChallengeFrame(info.id)\n            if (!info.hasChallengeFrame) {\n              info._type = 'score'\n            }\n          }\n          return info\n        })\n    } catch (error) {\n      result.error = error\n      return result\n    }\n    this.log('findRecaptchas - result', {\n      captchaNum: result.captchas.length,\n      result\n    })\n    return result\n  }\n\n  public async enterRecaptchaSolutions() {\n    const result = {\n      solved: [] as (types.CaptchaSolved | undefined)[],\n      error: null as any\n    }\n    try {\n      await this._waitUntilDocumentReady()\n      const clients = this.getClients()\n      this.log('enterRecaptchaSolutions', {\n        url: document.location.href,\n        hasClients: !!clients,\n        solutionNum: this.data.solutions.length\n      })\n\n      if (!clients) {\n        result.error = 'No recaptchas found'\n        return result\n      }\n      const solutions = this.data.solutions\n      if (!solutions || !solutions.length) {\n        result.error = 'No solutions provided'\n        return result\n      }\n\n      result.solved = this.data.solutions.map(solution => {\n        const client = this.getClientById(solution.id)\n        this.log(' - client', !!client)\n        const solved: types.CaptchaSolved = {\n          _vendor: 'recaptcha',\n          id: client.id,\n          responseElement: false,\n          responseCallback: false\n        }\n        const $iframe = this._findVisibleIframeNodeById(solved.id)\n        this.log(' - $iframe', !!$iframe)\n        if (!$iframe) {\n          solved.error = `Iframe not found for id '${solved.id}'`\n          return solved\n        }\n\n        if (this.hasActiveChallengePopup(solved.id)) {\n          // Hide if present challenge window\n          this._hideChallengeWindowIfPresent(solved.id)\n        }\n\n        // Enter solution in response textarea\n        const $input = this.getResponseInputById(solved.id)\n        this.log(' - $input', !!$input)\n        if ($input) {\n          $input.innerHTML = solution.text\n          solved.responseElement = true\n        }\n        // Enter solution in optional callback\n        this.log(' - callback', !!client.callback)\n        if (client.callback) {\n          try {\n            this.log(' - callback - type', {\n              typeof: typeof client.callback,\n              value: '' + client.callback\n            })\n            if (typeof client.callback === 'function') {\n              client.callback.call(window, solution.text)\n            } else {\n              eval(client.callback).call(window, solution.text) // tslint:disable-line\n              this.log(' - callback - aftereval')\n            }\n            solved.responseCallback = true\n          } catch (error) {\n            solved.error = error\n          }\n        }\n        // Finishing up\n        solved.isSolved = solved.responseCallback || solved.responseElement\n        solved.solvedAt = new Date()\n        this._paintCaptchaSolved($iframe)\n        this.log(' - solved', solved)\n        return solved\n      })\n    } catch (error) {\n      result.error = error\n      return result\n    }\n    this.log('enterRecaptchaSolutions - finished', result)\n    return result\n  }\n}\n\n/*\n// Example data\n\n{\n    \"captchas\": [{\n        \"sitekey\": \"6LdAUwoUAAAAAH44X453L0tUWOvx11XXXXXXXX\",\n        \"id\": \"lnfy52r0cccc\",\n        \"widgetId\": 0,\n        \"display\": {\n            \"size\": null,\n            \"top\": 23,\n            \"left\": 13,\n            \"width\": 28,\n            \"height\": 28,\n            \"theme\": null\n        },\n        \"url\": \"https://example.com\",\n        \"hasResponseElement\": true\n    }],\n    \"error\": null\n}\n\n{\n    \"solutions\": [{\n        \"id\": \"lnfy52r0cccc\",\n        \"provider\": \"2captcha\",\n        \"providerCaptchaId\": \"61109548000\",\n        \"text\": \"03AF6jDqVSOVODT-wLKZ47U0UXz...\",\n        \"requestAt\": \"2019-02-09T18:30:43.587Z\",\n        \"responseAt\": \"2019-02-09T18:30:57.937Z\"\n    }]\n    \"error\": null\n}\n\n{\n    \"solved\": [{\n        \"id\": \"lnfy52r0cccc\",\n        \"responseElement\": true,\n        \"responseCallback\": false,\n        \"isSolved\": true,\n        \"solvedAt\": {}\n    }]\n    \"error\": null\n}\n*/\n", "import * as types from './types'\n\nexport const ContentScriptDefaultOpts: types.ContentScriptOpts = {\n  visualFeedback: true\n}\n\nexport const ContentScriptDefaultData: types.ContentScriptData = {\n  solutions: []\n}\n\n/**\n * Content script for Hcaptcha handling (runs in browser context)\n * @note External modules are not supported here (due to content script isolation)\n */\nexport class HcaptchaContentScript {\n  private opts: types.ContentScriptOpts\n  private data: types.ContentScriptData\n\n  private baseUrls = [\n    'assets.hcaptcha.com/captcha/v1/',\n    'newassets.hcaptcha.com/captcha/v1/',\n  ]\n\n  constructor(\n    opts = ContentScriptDefaultOpts,\n    data = ContentScriptDefaultData\n  ) {\n    // Workaround for https://github.com/esbuild-kit/tsx/issues/113\n    if (typeof globalThis.__name === 'undefined') {\n      globalThis.__defProp = Object.defineProperty\n      globalThis.__name = (target, value) =>\n        globalThis.__defProp(target, 'name', { value, configurable: true })\n    }\n\n    this.opts = opts\n    this.data = data\n  }\n\n  private async _waitUntilDocumentReady() {\n    return new Promise(function(resolve) {\n      if (!document || !window) return resolve(null)\n      const loadedAlready = /^loaded|^i|^c/.test(document.readyState)\n      if (loadedAlready) return resolve(null)\n\n      function onReady() {\n        resolve(null)\n        document.removeEventListener('DOMContentLoaded', onReady)\n        window.removeEventListener('load', onReady)\n      }\n\n      document.addEventListener('DOMContentLoaded', onReady)\n      window.addEventListener('load', onReady)\n    })\n  }\n\n  private _paintCaptchaBusy($iframe: HTMLIFrameElement) {\n    try {\n      if (this.opts.visualFeedback) {\n        $iframe.style.filter = `opacity(60%) hue-rotate(400deg)` // violet\n      }\n    } catch (error) {\n      // noop\n    }\n    return $iframe\n  }\n\n  /** Regular checkboxes */\n  private _findRegularCheckboxes() {\n    const nodeList = document.querySelectorAll<HTMLIFrameElement>(\n      this.baseUrls.map(url => `iframe[src*='${url}'][data-hcaptcha-widget-id]:not([src*='invisible'])`).join(',')\n    )\n    return Array.from(nodeList)\n  }\n\n  /** Find active challenges from invisible hcaptchas */\n  private _findActiveChallenges() {\n    const nodeList = document.querySelectorAll<HTMLIFrameElement>(\n      this.baseUrls.map(url => `div[style*='visible'] iframe[src*='${url}'][src*='hcaptcha.html']`).join(',')\n    )\n    return Array.from(nodeList)\n  }\n\n  private _extractInfoFromIframes(iframes: HTMLIFrameElement[]) {\n    return iframes\n      .map(el => el.src.replace('.html#', '.html?'))\n      .map(url => {\n        const { searchParams } = new URL(url)\n        const result: types.CaptchaInfo = {\n          _vendor: 'hcaptcha',\n          url: document.location.href,\n          id: searchParams.get('id'),\n          sitekey: searchParams.get('sitekey'),\n          display: {\n            size: searchParams.get('size') || 'normal'\n          }\n        }\n        return result\n      })\n  }\n\n  public async findRecaptchas() {\n    const result = {\n      captchas: [] as types.CaptchaInfo[],\n      error: null as null | Error\n    }\n    try {\n      await this._waitUntilDocumentReady()\n      const iframes = [\n        ...this._findRegularCheckboxes(),\n        ...this._findActiveChallenges()\n      ]\n      if (!iframes.length) {\n        return result\n      }\n      result.captchas = this._extractInfoFromIframes(iframes)\n      iframes.forEach(el => {\n        this._paintCaptchaBusy(el)\n      })\n    } catch (error) {\n      result.error = error\n      return result\n    }\n    return result\n  }\n\n  public async enterRecaptchaSolutions() {\n    const result = {\n      solved: [] as types.CaptchaSolved[],\n      error: null as any\n    }\n    try {\n      await this._waitUntilDocumentReady()\n\n      const solutions = this.data.solutions\n      if (!solutions || !solutions.length) {\n        result.error = 'No solutions provided'\n        return result\n      }\n      result.solved = solutions\n        .filter(solution => solution._vendor === 'hcaptcha')\n        .filter(solution => solution.hasSolution === true)\n        .map(solution => {\n          window.postMessage(\n            JSON.stringify({\n              id: solution.id,\n              label: 'challenge-closed',\n              source: 'hcaptcha',\n              contents: {\n                event: 'challenge-passed',\n                expiration: 120,\n                response: solution.text\n              }\n            }),\n            '*'\n          )\n          return {\n            _vendor: solution._vendor,\n            id: solution.id,\n            isSolved: true,\n            solvedAt: new Date()\n          }\n        })\n    } catch (error) {\n      result.error = error\n      return result\n    }\n    return result\n  }\n}\n", "// https://github.com/bochkarev-artem/2captcha/blob/master/index.js\n// TODO: Create our own API wrapper\n\nvar https = require('https')\nvar url = require('url')\nvar querystring = require('querystring')\n\nvar apiKey\nvar apiInUrl = 'https://2captcha.com/in.php'\nvar apiResUrl = 'https://2captcha.com/res.php'\nvar apiMethod = 'base64'\nvar SOFT_ID = '2589'\n\nvar defaultOptions = {\n  pollingInterval: 2000,\n  retries: 3\n}\n\nfunction pollCaptcha(captchaId, options, invalid, callback) {\n  invalid = invalid.bind({ options: options, captchaId: captchaId })\n  var intervalId = setInterval(function() {\n    var httpsRequestOptions = url.parse(\n      apiResUrl +\n        '?action=get&soft_id=' +\n        SOFT_ID +\n        '&key=' +\n        apiKey +\n        '&id=' +\n        captchaId\n    )\n    var request = https.request(httpsRequestOptions, function(response) {\n      var body = ''\n\n      response.on('data', function(chunk) {\n        body += chunk\n      })\n\n      response.on('end', function() {\n        if (body === 'CAPCHA_NOT_READY') {\n          return\n        }\n\n        clearInterval(intervalId)\n\n        var result = body.split('|')\n        if (result[0] !== 'OK') {\n          callback(result[0]) //error\n        } else {\n          callback(\n            null,\n            {\n              id: captchaId,\n              text: result[1]\n            },\n            invalid\n          )\n        }\n        callback = function() {} // prevent the callback from being called more than once, if multiple https requests are open at the same time.\n      })\n    })\n    request.on('error', function(e) {\n      request.destroy()\n      callback(e)\n    })\n    request.end()\n  }, options.pollingInterval || defaultOptions.pollingInterval)\n}\n\nexport const setApiKey = function(key) {\n  apiKey = key\n}\n\nexport const decode = function(base64, options, callback) {\n  if (!callback) {\n    callback = options\n    options = defaultOptions\n  }\n  var httpsRequestOptions = url.parse(apiInUrl)\n  httpsRequestOptions.method = 'POST'\n\n  var postData = {\n    method: apiMethod,\n    key: apiKey,\n    soft_id: SOFT_ID,\n    body: base64\n  }\n\n  postData = querystring.stringify(postData)\n\n  var request = https.request(httpsRequestOptions, function(response) {\n    var body = ''\n\n    response.on('data', function(chunk) {\n      body += chunk\n    })\n\n    response.on('end', function() {\n      var result = body.split('|')\n      if (result[0] !== 'OK') {\n        return callback(result[0])\n      }\n\n      pollCaptcha(\n        result[1],\n        options,\n        function(error) {\n          var callbackToInitialCallback = callback\n\n          report(this.captchaId)\n\n          if (error) {\n            return callbackToInitialCallback('CAPTCHA_FAILED')\n          }\n\n          if (!this.options.retries) {\n            this.options.retries = defaultOptions.retries\n          }\n          if (this.options.retries > 1) {\n            this.options.retries = this.options.retries - 1\n            decode(base64, this.options, callback)\n          } else {\n            callbackToInitialCallback('CAPTCHA_FAILED_TOO_MANY_TIMES')\n          }\n        },\n        callback\n      )\n    })\n  })\n  request.on('error', function(e) {\n    request.destroy()\n    callback(e)\n  })\n\n  request.write(postData)\n  request.end()\n}\n\nexport const decodeReCaptcha = function(\n  captchaMethod,\n  captcha,\n  pageUrl,\n  extraData,\n  options,\n  callback\n) {\n  if (!callback) {\n    callback = options\n    options = defaultOptions\n  }\n  var httpsRequestOptions = url.parse(apiInUrl)\n  httpsRequestOptions.method = 'POST'\n\n  var postData = {\n    method: captchaMethod,\n    key: apiKey,\n    soft_id: SOFT_ID,\n    // googlekey: captcha,\n    pageurl: pageUrl,\n    ...extraData\n  }\n  if (captchaMethod === 'userrecaptcha') {\n    postData.googlekey = captcha\n  }\n  if (captchaMethod === 'hcaptcha') {\n    postData.sitekey = captcha\n  }\n\n  postData = querystring.stringify(postData)\n\n  var request = https.request(httpsRequestOptions, function(response) {\n    var body = ''\n\n    response.on('data', function(chunk) {\n      body += chunk\n    })\n\n    response.on('end', function() {\n      var result = body.split('|')\n      if (result[0] !== 'OK') {\n        return callback(result[0])\n      }\n\n      pollCaptcha(\n        result[1],\n        options,\n        function(error) {\n          var callbackToInitialCallback = callback\n\n          report(this.captchaId)\n\n          if (error) {\n            return callbackToInitialCallback('CAPTCHA_FAILED')\n          }\n\n          if (!this.options.retries) {\n            this.options.retries = defaultOptions.retries\n          }\n          if (this.options.retries > 1) {\n            this.options.retries = this.options.retries - 1\n            decodeReCaptcha(\n              captchaMethod,\n              captcha,\n              pageUrl,\n              extraData,\n              this.options,\n              callback\n            )\n          } else {\n            callbackToInitialCallback('CAPTCHA_FAILED_TOO_MANY_TIMES')\n          }\n        },\n        callback\n      )\n    })\n  })\n  request.on('error', function(e) {\n    request.destroy()\n    callback(e)\n  })\n  request.write(postData)\n  request.end()\n}\n\nexport const decodeUrl = function(uri, options, callback) {\n  if (!callback) {\n    callback = options\n    options = defaultOptions\n  }\n\n  var options = url.parse(uri)\n\n  var request = https.request(options, function(response) {\n    var body = ''\n    response.setEncoding('base64')\n\n    response.on('data', function(chunk) {\n      body += chunk\n    })\n\n    response.on('end', function() {\n      decode(body, options, callback)\n    })\n  })\n  request.on('error', function(e) {\n    request.destroy()\n    callback(e)\n  })\n  request.end()\n}\n\nexport const solveRecaptchaFromHtml = function(html, options, callback) {\n  if (!callback) {\n    callback = options\n    options = defaultOptions\n  }\n  var googleUrl = html.split('/challenge?k=')\n  if (googleUrl.length < 2) return callback('No captcha found in html')\n  googleUrl = googleUrl[1]\n  googleUrl = googleUrl.split('\"')[0]\n  googleUrl = googleUrl.split(\"'\")[0]\n  googleUrl = 'https://www.google.com/recaptcha/api/challenge?k=' + googleUrl\n\n  var httpsRequestOptions = url.parse(googleUrl)\n\n  var request = https.request(httpsRequestOptions, function(response) {\n    var body = ''\n    response.on('data', function(chunk) {\n      body += chunk\n    })\n\n    response.on('end', function() {\n      var challengeArr = body.split(\"'\")\n      if (!challengeArr[1]) return callback('Parsing captcha failed')\n      var challenge = challengeArr[1]\n      if (challenge.length === 0) return callback('Parsing captcha failed')\n\n      decodeUrl(\n        'https://www.google.com/recaptcha/api/image?c=' + challenge,\n        options,\n        function(error, result, invalid) {\n          if (result) {\n            result.challenge = challenge\n          }\n          callback(error, result, invalid)\n        }\n      )\n    })\n  })\n  request.end()\n}\n\nexport const report = function(captchaId) {\n  var reportUrl =\n    apiResUrl +\n    '?action=reportbad&soft_id=' +\n    SOFT_ID +\n    '&key=' +\n    apiKey +\n    '&id=' +\n    captchaId\n  var options = url.parse(reportUrl)\n\n  var request = https.request(options, function(response) {\n    // var body = ''\n    // response.on('data', function(chunk) {\n    //   body += chunk\n    // })\n    // response.on('end', function() {})\n  })\n  request.end()\n}\n", "export const PROVIDER_ID = '2captcha'\nimport * as types from '../types'\n\nimport Debug from 'debug'\nconst debug = Debug(`puppeteer-extra-plugin:recaptcha:${PROVIDER_ID}`)\n\n// const solver = require('./2captcha-api')\nimport * as solver from './2captcha-api'\n\nconst secondsBetweenDates = (before: Date, after: Date) =>\n  (after.getTime() - before.getTime()) / 1000\n\nexport interface DecodeRecaptchaAsyncResult {\n  err?: any\n  result?: any\n  invalid?: any\n}\n\nexport interface TwoCaptchaProviderOpts {\n  useEnterpriseFlag?: boolean\n  useActionValue?: boolean\n}\n\nconst providerOptsDefaults: TwoCaptchaProviderOpts = {\n  useEnterpriseFlag: false, // Seems to make solving chance worse?\n  useActionValue: true\n}\n\nasync function decodeRecaptchaAsync(\n  token: string,\n  vendor: types.CaptchaVendor,\n  sitekey: string,\n  url: string,\n  extraData: any,\n  opts = { pollingInterval: 2000 }\n): Promise<DecodeRecaptchaAsyncResult> {\n  return new Promise(resolve => {\n    const cb = (err: any, result: any, invalid: any) =>\n      resolve({ err, result, invalid })\n    try {\n      solver.setApiKey(token)\n\n      let method = 'userrecaptcha'\n      if (vendor === 'hcaptcha') {\n        method = 'hcaptcha'\n      }\n      solver.decodeReCaptcha(method, sitekey, url, extraData, opts, cb)\n    } catch (error) {\n      return resolve({ err: error })\n    }\n  })\n}\n\nexport async function getSolutions(\n  captchas: types.CaptchaInfo[] = [],\n  token: string = '',\n  opts: TwoCaptchaProviderOpts = {}\n): Promise<types.GetSolutionsResult> {\n  opts = { ...providerOptsDefaults, ...opts }\n  const solutions = await Promise.all(\n    captchas.map(c => getSolution(c, token, opts))\n  )\n  return { solutions, error: solutions.find(s => !!s.error) }\n}\n\nasync function getSolution(\n  captcha: types.CaptchaInfo,\n  token: string,\n  opts: TwoCaptchaProviderOpts\n): Promise<types.CaptchaSolution> {\n  const solution: types.CaptchaSolution = {\n    _vendor: captcha._vendor,\n    provider: PROVIDER_ID\n  }\n  try {\n    if (!captcha || !captcha.sitekey || !captcha.url || !captcha.id) {\n      throw new Error('Missing data in captcha')\n    }\n    solution.id = captcha.id\n    solution.requestAt = new Date()\n    debug('Requesting solution..', solution)\n    const extraData = {}\n    if (captcha.s) {\n      extraData['data-s'] = captcha.s // google site specific property\n    }\n    if (opts.useActionValue && captcha.action) {\n      extraData['action'] = captcha.action // Optional v3/enterprise action\n    }\n    if (opts.useEnterpriseFlag && captcha.isEnterprise) {\n      extraData['enterprise'] = 1\n    }\n    \n    if (process.env['2CAPTCHA_PROXY_TYPE'] && process.env['2CAPTCHA_PROXY_ADDRESS']) {\n         extraData['proxytype'] = process.env['2CAPTCHA_PROXY_TYPE'].toUpperCase()\n         extraData['proxy'] = process.env['2CAPTCHA_PROXY_ADDRESS']\n    }\n      \n    const { err, result, invalid } = await decodeRecaptchaAsync(\n      token,\n      captcha._vendor,\n      captcha.sitekey,\n      captcha.url,\n      extraData\n    )\n    debug('Got response', { err, result, invalid })\n    if (err) throw new Error(`${PROVIDER_ID} error: ${err}`)\n    if (!result || !result.text || !result.id) {\n      throw new Error(`${PROVIDER_ID} error: Missing response data: ${result}`)\n    }\n    solution.providerCaptchaId = result.id\n    solution.text = result.text\n    solution.responseAt = new Date()\n    solution.hasSolution = !!solution.text\n    solution.duration = secondsBetweenDates(\n      solution.requestAt,\n      solution.responseAt\n    )\n  } catch (error) {\n    debug('Error', error)\n    solution.error = error.toString()\n  }\n  return solution\n}\n", "import { PuppeteerExtraPlugin } from 'puppeteer-extra-plugin'\n\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'puppeteer'\n\nimport * as types from './types'\n\nimport { RecaptchaContentScript } from './content'\nimport { HcaptchaContentScript } from './content-hcaptcha'\nimport * as TwoCaptcha from './provider/2captcha'\n\nexport const BuiltinSolutionProviders: types.SolutionProvider[] = [\n  {\n    id: TwoCaptcha.PROVIDER_ID,\n    fn: TwoCaptcha.getSolutions\n  }\n]\n\n/**\n * A puppeteer-extra plugin to automatically detect and solve reCAPTCHAs.\n * @noInheritDoc\n */\nexport class PuppeteerExtraPluginRecaptcha extends PuppeteerExtraPlugin {\n  private contentScriptDebug: debug.Debugger\n\n  constructor(opts: Partial<types.PluginOptions>) {\n    super(opts)\n    this.debug('Initialized', this.opts)\n\n    this.contentScriptDebug = this.debug.extend('cs')\n  }\n\n  get name() {\n    return 'recaptcha'\n  }\n\n  get defaults(): types.PluginOptions {\n    return {\n      visualFeedback: true,\n      throwOnError: false,\n      solveInViewportOnly: false,\n      solveScoreBased: false,\n      solveInactiveChallenges: false\n    }\n  }\n\n  get opts(): types.PluginOptions {\n    return super.opts as any\n  }\n\n  get contentScriptOpts(): types.ContentScriptOpts {\n    const { visualFeedback } = this.opts\n    return {\n      visualFeedback,\n      debugBinding: this.contentScriptDebug.enabled\n        ? this.debugBindingName\n        : undefined\n    }\n  }\n\n  /** An optional global window object we use for contentscript debug logging */\n  private debugBindingName = '___pepr_cs'\n\n  private _generateContentScript(\n    vendor: types.CaptchaVendor,\n    fn: 'findRecaptchas' | 'enterRecaptchaSolutions',\n    data?: any\n  ) {\n    this.debug('_generateContentScript', vendor, fn, data)\n    let scriptSource = RecaptchaContentScript.toString()\n    let scriptName = 'RecaptchaContentScript'\n    if (vendor === 'hcaptcha') {\n      scriptSource = HcaptchaContentScript.toString()\n      scriptName = 'HcaptchaContentScript'\n    }\n    // Some bundlers transform classes to anonymous classes that are assigned to\n    // vars (e.g. esbuild). In such cases, `unexpected token '{'` errors are thrown\n    // once the script is executed. Let's bring class name back to script in such\n    // cases!\n    scriptSource = scriptSource.replace(/class \\{/, `class ${scriptName} {`)\n    return `(async() => {\n      const DATA = ${JSON.stringify(data || null)}\n      const OPTS = ${JSON.stringify(this.contentScriptOpts)}\n\n      ${scriptSource}\n      const script = new ${scriptName}(OPTS, DATA)\n      return script.${fn}()\n    })()`\n  }\n\n  /** Based on the user defined options we may want to filter out certain captchas (inactive, etc) */\n  private _filterRecaptchas(recaptchas: types.CaptchaInfo[] = []) {\n    const results = recaptchas.map((c: types.FilteredCaptcha) => {\n      if (\n        c._type === 'invisible' &&\n        !c.hasActiveChallengePopup &&\n        !this.opts.solveInactiveChallenges\n      ) {\n        c.filtered = true\n        c.filteredReason = 'solveInactiveChallenges'\n      }\n      if (c._type === 'score' && !this.opts.solveScoreBased) {\n        c.filtered = true\n        c.filteredReason = 'solveScoreBased'\n      }\n      if (\n        c._type === 'checkbox' &&\n        !c.isInViewport &&\n        this.opts.solveInViewportOnly\n      ) {\n        c.filtered = true\n        c.filteredReason = 'solveInViewportOnly'\n      }\n      if (c.filtered) {\n        this.debug('Filtered out captcha based on provided options', {\n          id: c.id,\n          reason: c.filteredReason,\n          captcha: c\n        })\n      }\n      return c\n    })\n    return {\n      captchas: results.filter(c => !c.filtered) as types.CaptchaInfo[],\n      filtered: results.filter(c => c.filtered)\n    }\n  }\n\n  async findRecaptchas(page: Page | Frame) {\n    this.debug('findRecaptchas')\n    // As this might be called very early while recaptcha is still loading\n    // we add some extra waiting logic for developer convenience.\n    const hasRecaptchaScriptTag = await page.$(\n      `script[src*=\"/recaptcha/api.js\"], script[src*=\"/recaptcha/enterprise.js\"]`\n    )\n    this.debug('hasRecaptchaScriptTag', !!hasRecaptchaScriptTag)\n    if (hasRecaptchaScriptTag) {\n      this.debug('waitForRecaptchaClient - start', new Date())\n      await page\n        .waitForFunction(\n          `\n        (function() {\n          return Object.keys((window.___grecaptcha_cfg || {}).clients || {}).length\n        })()\n      `,\n          { polling: 200, timeout: 10 * 1000 }\n        )\n        .catch(this.debug)\n      this.debug('waitForRecaptchaClient - end', new Date()) // used as timer\n    }\n    const hasHcaptchaScriptTag = await page.$(\n      `script[src*=\"hcaptcha.com/1/api.js\"]`\n    )\n    this.debug('hasHcaptchaScriptTag', !!hasHcaptchaScriptTag)\n    if (hasHcaptchaScriptTag) {\n      this.debug('wait:hasHcaptchaScriptTag - start', new Date())\n      await page.waitForFunction(\n        `\n        (function() {\n          return window.hcaptcha\n        })()\n      `,\n        { polling: 200, timeout: 10 * 1000 }\n      )\n      this.debug('wait:hasHcaptchaScriptTag - end', new Date()) // used as timer\n    }\n\n    const onDebugBindingCalled = (message: string, data: any) => {\n      this.contentScriptDebug(message, data)\n    }\n\n    if (this.contentScriptDebug.enabled) {\n      if ('exposeFunction' in page) {\n        await page.exposeFunction(this.debugBindingName, onDebugBindingCalled)\n      }\n    }\n    // Even without a recaptcha script tag we're trying, just in case.\n    const resultRecaptcha: types.FindRecaptchasResult = (await page.evaluate(\n      this._generateContentScript('recaptcha', 'findRecaptchas')\n    )) as any\n    const resultHcaptcha: types.FindRecaptchasResult = (await page.evaluate(\n      this._generateContentScript('hcaptcha', 'findRecaptchas')\n    )) as any\n\n    const filterResults = this._filterRecaptchas(resultRecaptcha.captchas)\n    this.debug(\n      `Filter results: ${filterResults.filtered.length} of ${filterResults.captchas.length} captchas filtered from results.`\n    )\n\n    const response: types.FindRecaptchasResult = {\n      captchas: [...filterResults.captchas, ...resultHcaptcha.captchas],\n      filtered: filterResults.filtered,\n      error: resultRecaptcha.error || resultHcaptcha.error\n    }\n    this.debug('findRecaptchas', response)\n    if (this.opts.throwOnError && response.error) {\n      throw new Error(response.error)\n    }\n    return response\n  }\n\n  async getRecaptchaSolutions(\n    captchas: types.CaptchaInfo[],\n    provider?: types.SolutionProvider\n  ) {\n    this.debug('getRecaptchaSolutions', { captchaNum: captchas.length })\n    provider = provider || this.opts.provider\n    if (\n      !provider ||\n      (!provider.token && !provider.fn) ||\n      (provider.token && provider.token === 'XXXXXXX' && !provider.fn)\n    ) {\n      throw new Error('Please provide a solution provider to the plugin.')\n    }\n    let fn = provider.fn\n    if (!fn) {\n      const builtinProvider = BuiltinSolutionProviders.find(\n        p => p.id === (provider || {}).id\n      )\n      if (!builtinProvider || !builtinProvider.fn) {\n        throw new Error(\n          `Cannot find builtin provider with id '${provider.id}'.`\n        )\n      }\n      fn = builtinProvider.fn\n    }\n    const response = await fn.call(\n      this,\n      captchas,\n      provider.token,\n      provider.opts || {}\n    )\n    response.error =\n      response.error ||\n      response.solutions.find((s: types.CaptchaSolution) => !!s.error)\n    this.debug('getRecaptchaSolutions', response)\n    if (response && response.error) {\n      console.warn(\n        'PuppeteerExtraPluginRecaptcha: An error occured during \"getRecaptchaSolutions\":',\n        response.error\n      )\n    }\n    if (this.opts.throwOnError && response.error) {\n      throw new Error(response.error)\n    }\n    return response\n  }\n\n  async enterRecaptchaSolutions(\n    page: Page | Frame,\n    solutions: types.CaptchaSolution[]\n  ) {\n    this.debug('enterRecaptchaSolutions', { solutions })\n\n    const hasRecaptcha = !!solutions.find(s => s._vendor === 'recaptcha')\n    const solvedRecaptcha: types.EnterRecaptchaSolutionsResult = hasRecaptcha\n      ? ((await page.evaluate(\n          this._generateContentScript('recaptcha', 'enterRecaptchaSolutions', {\n            solutions\n          })\n        )) as any)\n      : { solved: [] }\n    const hasHcaptcha = !!solutions.find(s => s._vendor === 'hcaptcha')\n    const solvedHcaptcha: types.EnterRecaptchaSolutionsResult = hasHcaptcha\n      ? ((await page.evaluate(\n          this._generateContentScript('hcaptcha', 'enterRecaptchaSolutions', {\n            solutions\n          })\n        )) as any)\n      : { solved: [] }\n\n    const response: types.EnterRecaptchaSolutionsResult = {\n      solved: [...solvedRecaptcha.solved, ...solvedHcaptcha.solved],\n      error: solvedRecaptcha.error || solvedHcaptcha.error\n    }\n    response.error = response.error || response.solved.find(s => !!s.error)\n    this.debug('enterRecaptchaSolutions', response)\n    if (this.opts.throwOnError && response.error) {\n      throw new Error(response.error)\n    }\n    return response\n  }\n\n  async solveRecaptchas(\n    page: Page | Frame\n  ): Promise<types.SolveRecaptchasResult> {\n    this.debug('solveRecaptchas')\n    const response: types.SolveRecaptchasResult = {\n      captchas: [],\n      filtered: [],\n      solutions: [],\n      solved: [],\n      error: null\n    }\n    try {\n      // If `this.opts.throwOnError` is set any of the\n      // following will throw and abort execution.\n      const {\n        captchas,\n        filtered,\n        error: captchasError\n      } = await this.findRecaptchas(page)\n      response.captchas = captchas\n      response.filtered = filtered\n\n      if (captchas.length) {\n        const {\n          solutions,\n          error: solutionsError\n        } = await this.getRecaptchaSolutions(response.captchas)\n        response.solutions = solutions\n\n        const {\n          solved,\n          error: solvedError\n        } = await this.enterRecaptchaSolutions(page, response.solutions)\n        response.solved = solved\n\n        response.error = captchasError || solutionsError || solvedError\n      }\n    } catch (error) {\n      response.error = error.toString()\n    }\n    this.debug('solveRecaptchas', response)\n    if (this.opts.throwOnError && response.error) {\n      throw new Error(response.error)\n    }\n    return response\n  }\n\n  private _addCustomMethods(prop: Page | Frame) {\n    prop.findRecaptchas = async () => this.findRecaptchas(prop)\n    prop.getRecaptchaSolutions = async (\n      captchas: types.CaptchaInfo[],\n      provider?: types.SolutionProvider\n    ) => this.getRecaptchaSolutions(captchas, provider)\n    prop.enterRecaptchaSolutions = async (solutions: types.CaptchaSolution[]) =>\n      this.enterRecaptchaSolutions(prop, solutions)\n    // Add convenience methods that wraps all others\n    prop.solveRecaptchas = async () => this.solveRecaptchas(prop)\n  }\n\n  async onPageCreated(page: Page) {\n    this.debug('onPageCreated', page.url())\n    // Make sure we can run our content script\n    await page.setBypassCSP(true)\n\n    // Add custom page methods\n    this._addCustomMethods(page)\n\n    // Add custom methods to potential frames as well\n    page.on('frameattached', frame => {\n      if (!frame) return\n      this._addCustomMethods(frame)\n    })\n  }\n\n  /** Add additions to already existing pages and frames */\n  async onBrowser(browser: Browser) {\n    const pages = await browser.pages()\n    for (const page of pages) {\n      this._addCustomMethods(page)\n      for (const frame of page.mainFrame().childFrames()) {\n        this._addCustomMethods(frame)\n      }\n    }\n  }\n}\n\n/** Default export, PuppeteerExtraPluginRecaptcha  */\nconst defaultExport = (options?: Partial<types.PluginOptions>) => {\n  return new PuppeteerExtraPluginRecaptcha(options || {})\n}\n\nexport default defaultExport\n"], "names": ["ContentScriptDefaultOpts", "ContentScriptDefaultData", "solver.<PERSON><PERSON><PERSON><PERSON><PERSON>", "solver.decodeReCaptcha", "TwoCaptcha.PROVIDER_ID", "TwoCaptcha.getSolutions"], "mappings": ";;;;;;;;AAEO,MAAM,wBAAwB,GAA4B;IAC/D,cAAc,EAAE,IAAI;IACpB,YAAY,EAAE,SAAS;CACxB,CAAA;AAEM,MAAM,wBAAwB,GAA4B;IAC/D,SAAS,EAAE,EAAE;CACd,CAAA;AAOD;;;;MAIa,sBAAsB;IAKjC,YACE,IAAI,GAAG,wBAAwB,EAC/B,IAAI,GAAG,wBAAwB;;QAgBzB,QAAG,GAAG,CAAC,OAAe,EAAE,IAAU;YACxC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBAChE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;aAClE;SACF,CAAA;;QAGO,UAAK,GAAG,CAAC,KAAY,KAAK,CAAC,CAAM,KACvC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,sCAAW,CAAC,KAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAG,EAAE,EAAE,CAAC,CAAA;;QAG3C,eAAU,GAAG,CAAC,IAAS,KAC7B,CAAC,EACC,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,YAAY;aAChB,OAAO,IAAI,CAAC,cAAc,KAAK,UAAU;gBACxC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAChC,CAAA;;QA9BD,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE;YAC5C,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAA;YAC5C,UAAU,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,KAAK,KAChC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;SACtE;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAChD,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;KACzE;;IAuBO,aAAa,CAAC,IAAS;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACzC,QACE,IAAI,CAAC,GAAG,IAAI,CAAC;YACb,IAAI,CAAC,IAAI,IAAI,CAAC;YACd,IAAI,CAAC,MAAM;iBACR,MAAM,CAAC,WAAW;qBAChB,QAAQ,CAAC,eAAe,CAAC,YAAY;wBACpC,IAAI,CAAC,KAAK;6BACP,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EACtE;KACF;;;IAIO,cAAc,CAAC,IAAS,EAAE,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI;QAC7D,MAAM,QAAQ,GAAG,CAAC,CAAM,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAA;QACvD,MAAM,MAAM,GAAG,CAAC,CAAM,KAAK,CAAC,IAAI,CAAC,YAAY,WAAW,CAAA;QACxD,IAAI,MAAM,GAAG,EAAS,CAAA;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAA;YACjD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG;gBAC3B,IAAI,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAAE,OAAM;gBAC3C,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;oBACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ;wBACrC,IAAI,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;4BAAE,OAAM;wBACrD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;8BACzC,OAAO,GAAG,IAAI,QAAQ,EAAE;8BACxB,GAAG,QAAQ,EAAE,CAAA;wBACjB,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;qBACtC,CAAC,CAAA;iBACH;qBAAM;oBACL,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;iBACxB;aACF,CAAC,CAAA;SACH;QACD,OAAO,MAAM,CAAA;KACd;;IAGO,cAAc,CAAC,MAAW,EAAE,KAAU;QAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAA;KAC9D;IAEO,MAAM,uBAAuB;QACnC,OAAO,IAAI,OAAO,CAAC,UAAS,OAAO;YACjC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE;gBACxB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;aACrB;YACD,MAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAC/D,IAAI,aAAa,EAAE;gBACjB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;aACrB;YAED,SAAS,OAAO;gBACd,OAAO,CAAC,IAAI,CAAC,CAAA;gBACb,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;gBACzD,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;aAC5C;YAED,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YACtD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;SACzC,CAAC,CAAA;KACH;IAEO,iBAAiB,CAAC,OAA0B;QAClD,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC5B,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAiC,CAAA;aACzD;SACF;QAAC,OAAO,KAAK,EAAE;;SAEf;QACD,OAAO,OAAO,CAAA;KACf;IAEO,mBAAmB,CAAC,OAA0B;QACpD,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC5B,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAiC,CAAA;aACzD;SACF;QAAC,OAAO,KAAK,EAAE;;SAEf;QACD,OAAO,OAAO,CAAA;KACf;IAEO,uBAAuB;QAC7B,OAAO,KAAK,CAAC,IAAI,CACf,QAAQ,CAAC,gBAAgB,CACvB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC;SACzC,CACF,CAAA;KACF;IACO,0BAA0B,CAAC,EAAW;QAC5C,OAAO,QAAQ,CAAC,aAAa,CAC3B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC,CACzC,CAAA;KACF;IAEO,6BAA6B,CAAC,KAAa,EAAE;QACnD,IAAI,KAAK,GAAuB,QAAQ,CAAC,aAAa,CACpD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC,CACzC,CAAA;QACD,IAAI,CAAC,GAAG,CAAC,kCAAkC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;QACvE,IAAI,CAAC,KAAK,EAAE;YACV,OAAM;SACP;QACD,OACE,KAAK;YACL,KAAK,CAAC,aAAa;YACnB,KAAK,CAAC,aAAa,KAAK,QAAQ,CAAC,IAAI,EACrC;YACA,KAAK,GAAG,KAAK,CAAC,aAAa,CAAA;SAC5B;QACD,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAA;SAClC;KACF;;IAGO,qBAAqB;QAC3B,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAChC,MAAM,KAAK,GAAG;YACZ,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,mBAAmB;SACpB,CAAA;QACD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,IAClC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,EAAE,CAAC,CACxC,CAAA;QACD,MAAM,KAAK,GAAG;YACZ,MAAM,EAAE,CAAC,wBAAwB,EAAE,8BAA8B,CAAC;YAClE,MAAM,EAAE,CAAC,wBAAwB,EAAE,8BAA8B,CAAC;SACnE,CAAA;QACD,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,IAC5B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC,CAC7C;YACD,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,IAC5B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC,CAC7C;SACF,CAAA;KACF;IAEO,qBAAqB,CAAC,OAA4B,QAAQ,EAAE,EAAE,GAAG,EAAE;QACzE,MAAM,UAAU,GAAG,IAAI,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAA;QAChD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;aAC3B,GAAG,CAAC,GAAG,IAAI,gBAAgB,GAAG,aAAa,UAAU,IAAI,EAAE,IAAI,CAAC;aAChE,IAAI,CAAC,GAAG,CAAC,CAAA;KACb;IAEO,UAAU;;QAEhB,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,yBAAyB;YAAE,OAAM;QACxD,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAClE,OAAM;SACP;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM;YAAE,OAAM;QACjE,OAAO,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAA;KACxC;IAEO,oBAAoB;;QAE1B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;aAC1C,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aACjC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;aACrC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aAC3C,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACxC,GAAG,CACF,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvC;aACA,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAA;QACnB,IAAI,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAA;QACxC,OAAO,MAAM,CAAA;KACd;;IAGO,sBAAsB;;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;aAC1C,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aAC3C,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACxC,GAAG,CACF,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvC;aACA,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;aAChB,MAAM,CACL,EAAE,IACA,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;aAChE,MAAM,CACZ,CAAA;QACH,IAAI,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAA;QAC1C,OAAO,MAAM,CAAA;KACd;IAEO,aAAa;;QAEnB,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,oBAAoB,EAAE;YAC9B,GAAG,IAAI,CAAC,sBAAsB,EAAE;SACjC,CAAA;QACD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;;QAElC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;QACxC,OAAO,KAAK,CAAA;KACb;IAEO,mBAAmB,CAAC,EAAW;QACrC,IAAI,CAAC,EAAE;YAAE,OAAO,KAAK,CAAA;;QAErB,MAAM,MAAM,GAAG,iDAAiD,CAAA;QAChE,MAAM,aAAa,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,CAAA;QAChE,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvE,OAAO,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;KAC1D;IAEO,WAAW,CAAC,EAAW;QAC7B,IAAI,CAAC,EAAE;YAAE,OAAO,KAAK,CAAA;QACrB,MAAM,QAAQ,GAAG,sDAAsD,EAAE,4BAA4B,CAAA;QACrG,OAAO,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;KACtD;;IAGO,uBAAuB,CAAC,EAAW;QACzC,IAAI,CAAC,EAAE;YAAE,OAAO,KAAK,CAAA;QACrB,MAAM,QAAQ,GAAG,sDAAsD,EAAE,IAAI,CAAA;QAC7E,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC7C,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;KAChC;;IAGO,iBAAiB,CAAC,EAAW;QACnC,IAAI,CAAC,EAAE;YAAE,OAAO,KAAK,CAAA;QACrB,QACE,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;aAChE,MAAM,GAAG,CAAC,EACd;KACF;IAEO,YAAY,CAAC,EAAW;QAC9B,IAAI,CAAC,EAAE;YAAE,OAAM;QACf,MAAM,MAAM,GAAG,0BAA0B,CAAA;QACzC,MAAM,aAAa,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,CAAA;QAChE,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvE,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,CAAA;QACjD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;KAChC;IAEO,oBAAoB,CAAC,EAAW;QACtC,IAAI,CAAC,EAAE;YAAE,OAAM;QACf,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO;YAAE,OAAM;QACpB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC3C,IAAI,WAAW,EAAE;YACf,OAAO,WAAW,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAA;SAClE;;;QAGD,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE;YAC7B,OAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAA;SACpE;KACF;IAEO,aAAa,CAAC,EAAW;QAC/B,IAAI,CAAC,EAAE;YAAE,OAAM;QACf,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;;QAEjC,IAAI,MAAM,GAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;aAC3C,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;aAC3C,KAAK,EAAE,CAAA;QACV,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAA;QAChE,IAAI,CAAC,MAAM;YAAE,OAAM;QACnB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAQ,CAAA;YAC3C,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;YAC3B,MAAM,CAAC,EAAE,GAAG,EAAE,CAAA;YACd,IAAI,CAAC,GAAG,CAAC,iCAAiC,EAAE;gBAC1C,EAAE;gBACF,SAAS,EAAE,CAAC,CAAC,MAAM;aACpB,CAAC,CAAA;SACH;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;SAC1D;QACD,OAAO,MAAM,CAAA;KACd;IAEO,qBAAqB,CAAC,MAAY;QACxC,IAAI,CAAC,MAAM;YAAE,OAAM;QACnB,MAAM,IAAI,GAAsB,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QAC3E,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAM;QACzB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAA;QAC1B,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAA;QACnB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;QACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,MAAM;YACN,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,OAAO;SACR,CAAC,CAAC,MAAM,CAAC,CAAA;QACV,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;SAC5B;;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;YACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAA;SAClD;QACD,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ;YAAE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAA;QACpE,OAAO,IAAI,CAAA;KACZ;IAEM,MAAM,cAAc;QACzB,MAAM,MAAM,GAAG;YACb,QAAQ,EAAE,EAAuC;YACjD,KAAK,EAAE,IAAW;SACnB,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YACjC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE;gBACzB,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBAC3B,UAAU,EAAE,CAAC,CAAC,OAAO;aACtB,CAAC,CAAA;YACF,IAAI,CAAC,OAAO;gBAAE,OAAO,MAAM,CAAA;YAC3B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE;iBACnC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;iBACjC,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;iBACjD,GAAG,CAAC,IAAI;gBACP,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;gBAClC,IAAI,CAAC,IAAI;oBAAE,OAAM;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACjD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAA;gBAClC,OAAO,IAAI,CAAA;aACZ,CAAC;iBACD,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;iBACxC,GAAG,CAAC,IAAI;gBACP,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;gBAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC5C,IAAI,CAAC,KAAK,GAAG,UAAU,CAAA;gBACvB,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,IAAI,CAAC,KAAK,GAAG,WAAW,CAAA;oBACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;oBACpE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;oBACxD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;wBAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAA;qBACrB;iBACF;gBACD,OAAO,IAAI,CAAA;aACZ,CAAC,CAAA;SACL;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE;YAClC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM;YAClC,MAAM;SACP,CAAC,CAAA;QACF,OAAO,MAAM,CAAA;KACd;IAEM,MAAM,uBAAuB;QAClC,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,EAAyC;YACjD,KAAK,EAAE,IAAW;SACnB,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YACjC,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE;gBAClC,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBAC3B,UAAU,EAAE,CAAC,CAAC,OAAO;gBACrB,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;aACxC,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,CAAC,KAAK,GAAG,qBAAqB,CAAA;gBACpC,OAAO,MAAM,CAAA;aACd;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;YACrC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACnC,MAAM,CAAC,KAAK,GAAG,uBAAuB,CAAA;gBACtC,OAAO,MAAM,CAAA;aACd;YAED,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;gBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;gBAC9C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;gBAC/B,MAAM,MAAM,GAAwB;oBAClC,OAAO,EAAE,WAAW;oBACpB,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,eAAe,EAAE,KAAK;oBACtB,gBAAgB,EAAE,KAAK;iBACxB,CAAA;gBACD,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC1D,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,OAAO,EAAE;oBACZ,MAAM,CAAC,KAAK,GAAG,4BAA4B,MAAM,CAAC,EAAE,GAAG,CAAA;oBACvD,OAAO,MAAM,CAAA;iBACd;gBAED,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;;oBAE3C,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;iBAC9C;;gBAGD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBACnD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;gBAC/B,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAA;oBAChC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAA;iBAC9B;;gBAED,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;gBAC1C,IAAI,MAAM,CAAC,QAAQ,EAAE;oBACnB,IAAI;wBACF,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE;4BAC7B,MAAM,EAAE,OAAO,MAAM,CAAC,QAAQ;4BAC9B,KAAK,EAAE,EAAE,GAAG,MAAM,CAAC,QAAQ;yBAC5B,CAAC,CAAA;wBACF,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,UAAU,EAAE;4BACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;yBAC5C;6BAAM;4BACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;4BACjD,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;yBACpC;wBACD,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAA;qBAC/B;oBAAC,OAAO,KAAK,EAAE;wBACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;qBACrB;iBACF;;gBAED,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,eAAe,CAAA;gBACnE,MAAM,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAA;gBAC5B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;gBAC7B,OAAO,MAAM,CAAA;aACd,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,IAAI,CAAC,GAAG,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAA;QACtD,OAAO,MAAM,CAAA;KACd;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzgBO,MAAMA,0BAAwB,GAA4B;IAC/D,cAAc,EAAE,IAAI;CACrB,CAAA;AAED,AAAO,MAAMC,0BAAwB,GAA4B;IAC/D,SAAS,EAAE,EAAE;CACd,CAAA;AAED;;;;AAIA,MAAa,qBAAqB;IAShC,YACE,IAAI,GAAGD,0BAAwB,EAC/B,IAAI,GAAGC,0BAAwB;QAPzB,aAAQ,GAAG;YACjB,iCAAiC;YACjC,oCAAoC;SACrC,CAAA;;QAOC,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,WAAW,EAAE;YAC5C,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAA;YAC5C,UAAU,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,KAAK,KAChC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA;SACtE;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;KACjB;IAEO,MAAM,uBAAuB;QACnC,OAAO,IAAI,OAAO,CAAC,UAAS,OAAO;YACjC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;gBAAE,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;YAC9C,MAAM,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YAC/D,IAAI,aAAa;gBAAE,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;YAEvC,SAAS,OAAO;gBACd,OAAO,CAAC,IAAI,CAAC,CAAA;gBACb,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;gBACzD,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;aAC5C;YAED,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YACtD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;SACzC,CAAC,CAAA;KACH;IAEO,iBAAiB,CAAC,OAA0B;QAClD,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC5B,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,iCAAiC,CAAA;aACzD;SACF;QAAC,OAAO,KAAK,EAAE;;SAEf;QACD,OAAO,OAAO,CAAA;KACf;;IAGO,sBAAsB;QAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,gBAAgB,GAAG,qDAAqD,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC7G,CAAA;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;KAC5B;;IAGO,qBAAqB;QAC3B,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,sCAAsC,GAAG,0BAA0B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACxG,CAAA;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;KAC5B;IAEO,uBAAuB,CAAC,OAA4B;QAC1D,OAAO,OAAO;aACX,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7C,GAAG,CAAC,GAAG;YACN,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;YACrC,MAAM,MAAM,GAAsB;gBAChC,OAAO,EAAE,UAAU;gBACnB,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBAC3B,EAAE,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;gBACpC,OAAO,EAAE;oBACP,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ;iBAC3C;aACF,CAAA;YACD,OAAO,MAAM,CAAA;SACd,CAAC,CAAA;KACL;IAEM,MAAM,cAAc;QACzB,MAAM,MAAM,GAAG;YACb,QAAQ,EAAE,EAAyB;YACnC,KAAK,EAAE,IAAoB;SAC5B,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,MAAM,OAAO,GAAG;gBACd,GAAG,IAAI,CAAC,sBAAsB,EAAE;gBAChC,GAAG,IAAI,CAAC,qBAAqB,EAAE;aAChC,CAAA;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnB,OAAO,MAAM,CAAA;aACd;YACD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;YACvD,OAAO,CAAC,OAAO,CAAC,EAAE;gBAChB,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;aAC3B,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,OAAO,MAAM,CAAA;KACd;IAEM,MAAM,uBAAuB;QAClC,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,EAA2B;YACnC,KAAK,EAAE,IAAW;SACnB,CAAA;QACD,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEpC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;YACrC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACnC,MAAM,CAAC,KAAK,GAAG,uBAAuB,CAAA;gBACtC,OAAO,MAAM,CAAA;aACd;YACD,MAAM,CAAC,MAAM,GAAG,SAAS;iBACtB,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,KAAK,UAAU,CAAC;iBACnD,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,CAAC;iBACjD,GAAG,CAAC,QAAQ;gBACX,MAAM,CAAC,WAAW,CAChB,IAAI,CAAC,SAAS,CAAC;oBACb,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,KAAK,EAAE,kBAAkB;oBACzB,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE;wBACR,KAAK,EAAE,kBAAkB;wBACzB,UAAU,EAAE,GAAG;wBACf,QAAQ,EAAE,QAAQ,CAAC,IAAI;qBACxB;iBACF,CAAC,EACF,GAAG,CACJ,CAAA;gBACD,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI,IAAI,EAAE;iBACrB,CAAA;aACF,CAAC,CAAA;SACL;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;YACpB,OAAO,MAAM,CAAA;SACd;QACD,OAAO,MAAM,CAAA;KACd;CACF;;ACxKD;AACA;AAEA,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;AAC5B,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;AACxB,IAAI,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;AAExC,IAAI,MAAM,CAAA;AACV,IAAI,QAAQ,GAAG,6BAA6B,CAAA;AAC5C,IAAI,SAAS,GAAG,8BAA8B,CAAA;AAC9C,AACA,IAAI,OAAO,GAAG,MAAM,CAAA;AAEpB,IAAI,cAAc,GAAG;IACnB,eAAe,EAAE,IAAI;IACrB,OAAO,EAAE,CAAC;CACX,CAAA;AAED,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;IACxD,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAA;IAClE,IAAI,UAAU,GAAG,WAAW,CAAC;QAC3B,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CACjC,SAAS;YACP,sBAAsB;YACtB,OAAO;YACP,OAAO;YACP,MAAM;YACN,MAAM;YACN,SAAS,CACZ,CAAA;QACD,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;YAChE,IAAI,IAAI,GAAG,EAAE,CAAA;YAEb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;gBAChC,IAAI,IAAI,KAAK,CAAA;aACd,CAAC,CAAA;YAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;gBACjB,IAAI,IAAI,KAAK,kBAAkB,EAAE;oBAC/B,OAAM;iBACP;gBAED,aAAa,CAAC,UAAU,CAAC,CAAA;gBAEzB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACtB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;iBACpB;qBAAM;oBACL,QAAQ,CACN,IAAI,EACJ;wBACE,EAAE,EAAE,SAAS;wBACb,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;qBAChB,EACD,OAAO,CACR,CAAA;iBACF;gBACD,QAAQ,GAAG,eAAa,CAAA;aACzB,CAAC,CAAA;SACH,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;YAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;YACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;SACZ,CAAC,CAAA;QACF,OAAO,CAAC,GAAG,EAAE,CAAA;KACd,EAAE,OAAO,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,CAAC,CAAA;AAC/D,CAAC;AAED,AAAO,MAAM,SAAS,GAAG,UAAS,GAAG;IACnC,MAAM,GAAG,GAAG,CAAA;AACd,CAAC,CAAA;AAED,AAiEO,MAAM,eAAe,GAAG,UAC7B,aAAa,EACb,OAAO,EACP,OAAO,EACP,SAAS,EACT,OAAO,EACP,QAAQ;IAER,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,OAAO,CAAA;QAClB,OAAO,GAAG,cAAc,CAAA;KACzB;IACD,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC7C,mBAAmB,CAAC,MAAM,GAAG,MAAM,CAAA;IAEnC,IAAI,QAAQ,mBACV,MAAM,EAAE,aAAa,EACrB,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,OAAO;;QAEhB,OAAO,EAAE,OAAO,IACb,SAAS,CACb,CAAA;IACD,IAAI,aAAa,KAAK,eAAe,EAAE;QACrC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAA;KAC7B;IACD,IAAI,aAAa,KAAK,UAAU,EAAE;QAChC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAA;KAC3B;IAED,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IAE1C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAS,QAAQ;QAChE,IAAI,IAAI,GAAG,EAAE,CAAA;QAEb,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAS,KAAK;YAChC,IAAI,IAAI,KAAK,CAAA;SACd,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACtB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;aAC3B;YAED,WAAW,CACT,MAAM,CAAC,CAAC,CAAC,EACT,OAAO,EACP,UAAS,KAAK;gBACZ,IAAI,yBAAyB,GAAG,QAAQ,CAAA;gBAExC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBAEtB,IAAI,KAAK,EAAE;oBACT,OAAO,yBAAyB,CAAC,gBAAgB,CAAC,CAAA;iBACnD;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACzB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAA;iBAC9C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE;oBAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAA;oBAC/C,eAAe,CACb,aAAa,EACb,OAAO,EACP,OAAO,EACP,SAAS,EACT,IAAI,CAAC,OAAO,EACZ,QAAQ,CACT,CAAA;iBACF;qBAAM;oBACL,yBAAyB,CAAC,+BAA+B,CAAC,CAAA;iBAC3D;aACF,EACD,QAAQ,CACT,CAAA;SACF,CAAC,CAAA;KACH,CAAC,CAAA;IACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;QAC5B,OAAO,CAAC,OAAO,EAAE,CAAA;QACjB,QAAQ,CAAC,CAAC,CAAC,CAAA;KACZ,CAAC,CAAA;IACF,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IACvB,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;AAED,AAoEO,MAAM,MAAM,GAAG,UAAS,SAAS;IACtC,IAAI,SAAS,GACX,SAAS;QACT,4BAA4B;QAC5B,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAA;IACX,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IAElC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,QAAQ;;;;;;KAMrD,CAAC,CAAA;IACF,OAAO,CAAC,GAAG,EAAE,CAAA;AACf,CAAC,CAAA;;ACtTM,MAAM,WAAW,GAAG,UAAU,CAAA;AAGrC,AACA,MAAM,KAAK,GAAG,KAAK,CAAC,oCAAoC,WAAW,EAAE,CAAC,CAAA;AAEtE,AAGA,MAAM,mBAAmB,GAAG,CAAC,MAAY,EAAE,KAAW,KACpD,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI,CAAA;AAa7C,MAAM,oBAAoB,GAA2B;IACnD,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,IAAI;CACrB,CAAA;AAED,eAAe,oBAAoB,CACjC,KAAa,EACb,MAA2B,EAC3B,OAAe,EACf,GAAW,EACX,SAAc,EACd,IAAI,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE;IAEhC,OAAO,IAAI,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,GAAG,CAAC,GAAQ,EAAE,MAAW,EAAE,OAAY,KAC7C,OAAO,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAA;QACnC,IAAI;YACFC,SAAgB,CAAC,KAAK,CAAC,CAAA;YAEvB,IAAI,MAAM,GAAG,eAAe,CAAA;YAC5B,IAAI,MAAM,KAAK,UAAU,EAAE;gBACzB,MAAM,GAAG,UAAU,CAAA;aACpB;YACDC,eAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;SAClE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;SAC/B;KACF,CAAC,CAAA;AACJ,CAAC;AAED,AAAO,eAAe,YAAY,CAChC,WAAgC,EAAE,EAClC,QAAgB,EAAE,EAClB,OAA+B,EAAE;IAEjC,IAAI,mCAAQ,oBAAoB,GAAK,IAAI,CAAE,CAAA;IAC3C,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAC/C,CAAA;IACD,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAA;AAC7D,CAAC;AAED,eAAe,WAAW,CACxB,OAA0B,EAC1B,KAAa,EACb,IAA4B;IAE5B,MAAM,QAAQ,GAA0B;QACtC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,QAAQ,EAAE,WAAW;KACtB,CAAA;IACD,IAAI;QACF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;SAC3C;QACD,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAA;QACxB,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;QAC/B,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAA;QACxC,MAAM,SAAS,GAAG,EAAE,CAAA;QACpB,IAAI,OAAO,CAAC,CAAC,EAAE;YACb,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,CAAC,CAAA;SAChC;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,EAAE;YACzC,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;SACrC;QACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,YAAY,EAAE;YAClD,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;SAC5B;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE;YAC5E,SAAS,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAA;YACzE,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;SAC9D;QAED,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,oBAAoB,CACzD,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,GAAG,EACX,SAAS,CACV,CAAA;QACD,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAA;QAC/C,IAAI,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,GAAG,WAAW,WAAW,GAAG,EAAE,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,GAAG,WAAW,kCAAkC,MAAM,EAAE,CAAC,CAAA;SAC1E;QACD,QAAQ,CAAC,iBAAiB,GAAG,MAAM,CAAC,EAAE,CAAA;QACtC,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;QAChC,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAA;QACtC,QAAQ,CAAC,QAAQ,GAAG,mBAAmB,CACrC,QAAQ,CAAC,SAAS,EAClB,QAAQ,CAAC,UAAU,CACpB,CAAA;KACF;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACrB,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;KAClC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;;MChHY,wBAAwB,GAA6B;IAChE;QACE,EAAE,EAAEC,WAAsB;QAC1B,EAAE,EAAEC,YAAuB;KAC5B;CACF,CAAA;AAED;;;;AAIA,MAAa,6BAA8B,SAAQ,oBAAoB;IAGrE,YAAY,IAAkC;QAC5C,KAAK,CAAC,IAAI,CAAC,CAAA;;QAmCL,qBAAgB,GAAG,YAAY,CAAA;QAlCrC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAEpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;KAClD;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAA;KACnB;IAED,IAAI,QAAQ;QACV,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,YAAY,EAAE,KAAK;YACnB,mBAAmB,EAAE,KAAK;YAC1B,eAAe,EAAE,KAAK;YACtB,uBAAuB,EAAE,KAAK;SAC/B,CAAA;KACF;IAED,IAAI,IAAI;QACN,OAAO,KAAK,CAAC,IAAW,CAAA;KACzB;IAED,IAAI,iBAAiB;QACnB,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,IAAI,CAAA;QACpC,OAAO;YACL,cAAc;YACd,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO;kBACzC,IAAI,CAAC,gBAAgB;kBACrB,SAAS;SACd,CAAA;KACF;IAKO,sBAAsB,CAC5B,MAA2B,EAC3B,EAAgD,EAChD,IAAU;QAEV,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QACtD,IAAI,YAAY,GAAG,sBAAsB,CAAC,QAAQ,EAAE,CAAA;QACpD,IAAI,UAAU,GAAG,wBAAwB,CAAA;QACzC,IAAI,MAAM,KAAK,UAAU,EAAE;YACzB,YAAY,GAAG,qBAAqB,CAAC,QAAQ,EAAE,CAAA;YAC/C,UAAU,GAAG,uBAAuB,CAAA;SACrC;;;;;QAKD,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,UAAU,IAAI,CAAC,CAAA;QACxE,OAAO;qBACU,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;qBAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC;;QAEnD,YAAY;2BACO,UAAU;sBACf,EAAE;SACf,CAAA;KACN;;IAGO,iBAAiB,CAAC,aAAkC,EAAE;QAC5D,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAwB;YACtD,IACE,CAAC,CAAC,KAAK,KAAK,WAAW;gBACvB,CAAC,CAAC,CAAC,uBAAuB;gBAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAClC;gBACA,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,yBAAyB,CAAA;aAC7C;YACD,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACrD,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,iBAAiB,CAAA;aACrC;YACD,IACE,CAAC,CAAC,KAAK,KAAK,UAAU;gBACtB,CAAC,CAAC,CAAC,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAC7B;gBACA,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjB,CAAC,CAAC,cAAc,GAAG,qBAAqB,CAAA;aACzC;YACD,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE;oBAC3D,EAAE,EAAE,CAAC,CAAC,EAAE;oBACR,MAAM,EAAE,CAAC,CAAC,cAAc;oBACxB,OAAO,EAAE,CAAC;iBACX,CAAC,CAAA;aACH;YACD,OAAO,CAAC,CAAA;SACT,CAAC,CAAA;QACF,OAAO;YACL,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAwB;YACjE,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;SAC1C,CAAA;KACF;IAED,MAAM,cAAc,CAAC,IAAkB;QACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;;;QAG5B,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,CAAC,CACxC,2EAA2E,CAC5E,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAA;QAC5D,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;YACxD,MAAM,IAAI;iBACP,eAAe,CACd;;;;OAIH,EACG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,CACrC;iBACA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;SACvD;QACD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,CAAC,CACvC,sCAAsC,CACvC,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAA;QAC1D,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;YAC3D,MAAM,IAAI,CAAC,eAAe,CACxB;;;;OAID,EACC,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,EAAE,CACrC,CAAA;YACD,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;SAC1D;QAED,MAAM,oBAAoB,GAAG,CAAC,OAAe,EAAE,IAAS;YACtD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACvC,CAAA;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACnC,IAAI,gBAAgB,IAAI,IAAI,EAAE;gBAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAA;aACvE;SACF;;QAED,MAAM,eAAe,IAAgC,MAAM,IAAI,CAAC,QAAQ,CACtE,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC3D,CAAQ,CAAA;QACT,MAAM,cAAc,IAAgC,MAAM,IAAI,CAAC,QAAQ,CACrE,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAC1D,CAAQ,CAAA;QAET,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QACtE,IAAI,CAAC,KAAK,CACR,mBAAmB,aAAa,CAAC,QAAQ,CAAC,MAAM,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,kCAAkC,CACvH,CAAA;QAED,MAAM,QAAQ,GAA+B;YAC3C,QAAQ,EAAE,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC;YACjE,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,KAAK,EAAE,eAAe,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;SACrD,CAAA;QACD,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;KAChB;IAED,MAAM,qBAAqB,CACzB,QAA6B,EAC7B,QAAiC;QAEjC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;QACpE,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACzC,IACE,CAAC,QAAQ;aACR,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;aAChC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAChE;YACA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;SACrE;QACD,IAAI,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAA;QACpB,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,eAAe,GAAG,wBAAwB,CAAC,IAAI,CACnD,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,EAAE,EAAE,CAClC,CAAA;YACD,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;gBAC3C,MAAM,IAAI,KAAK,CACb,yCAAyC,QAAQ,CAAC,EAAE,IAAI,CACzD,CAAA;aACF;YACD,EAAE,GAAG,eAAe,CAAC,EAAE,CAAA;SACxB;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,IAAI,CAC5B,IAAI,EACJ,QAAQ,EACR,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,IAAI,IAAI,EAAE,CACpB,CAAA;QACD,QAAQ,CAAC,KAAK;YACZ,QAAQ,CAAC,KAAK;gBACd,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAwB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QAClE,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAA;QAC7C,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC9B,OAAO,CAAC,IAAI,CACV,iFAAiF,EACjF,QAAQ,CAAC,KAAK,CACf,CAAA;SACF;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;KAChB;IAED,MAAM,uBAAuB,CAC3B,IAAkB,EAClB,SAAkC;QAElC,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;QAEpD,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,CAAA;QACrE,MAAM,eAAe,GAAwC,YAAY;eACnE,MAAM,IAAI,CAAC,QAAQ,CACnB,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,yBAAyB,EAAE;gBAClE,SAAS;aACV,CAAC,CACH;cACD,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;QAClB,MAAM,WAAW,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,CAAA;QACnE,MAAM,cAAc,GAAwC,WAAW;eACjE,MAAM,IAAI,CAAC,QAAQ,CACnB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,yBAAyB,EAAE;gBACjE,SAAS;aACV,CAAC,CACH;cACD,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;QAElB,MAAM,QAAQ,GAAwC;YACpD,MAAM,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC;YAC7D,KAAK,EAAE,eAAe,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;SACrD,CAAA;QACD,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACvE,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAA;QAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;KAChB;IAED,MAAM,eAAe,CACnB,IAAkB;QAElB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAC7B,MAAM,QAAQ,GAAgC;YAC5C,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,IAAI;SACZ,CAAA;QACD,IAAI;;;YAGF,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,KAAK,EAAE,aAAa,EACrB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACnC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAC5B,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAE5B,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,MAAM,EACJ,SAAS,EACT,KAAK,EAAE,cAAc,EACtB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACvD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAA;gBAE9B,MAAM,EACJ,MAAM,EACN,KAAK,EAAE,WAAW,EACnB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;gBAChE,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAA;gBAExB,QAAQ,CAAC,KAAK,GAAG,aAAa,IAAI,cAAc,IAAI,WAAW,CAAA;aAChE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;SAClC;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;QACvC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAChC;QACD,OAAO,QAAQ,CAAA;KAChB;IAEO,iBAAiB,CAAC,IAAkB;QAC1C,IAAI,CAAC,cAAc,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,qBAAqB,GAAG,OAC3B,QAA6B,EAC7B,QAAiC,KAC9B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACnD,IAAI,CAAC,uBAAuB,GAAG,OAAO,SAAkC,KACtE,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;;QAE/C,IAAI,CAAC,eAAe,GAAG,YAAY,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;KAC9D;IAED,MAAM,aAAa,CAAC,IAAU;QAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;;QAEvC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;;QAG7B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;;QAG5B,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK;YAC5B,IAAI,CAAC,KAAK;gBAAE,OAAM;YAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;SAC9B,CAAC,CAAA;KACH;;IAGD,MAAM,SAAS,CAAC,OAAgB;QAC9B,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,KAAK,EAAE,CAAA;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;YAC5B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;gBAClD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;KACF;CACF;AAED;AACA,MAAM,aAAa,GAAG,CAAC,OAAsC;IAC3D,OAAO,IAAI,6BAA6B,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;AACzD,CAAC,CAAA;;;;;"}