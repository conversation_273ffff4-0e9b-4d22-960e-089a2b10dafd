@echo off
title Create Desktop Shortcuts - Facebook Marketplace Automation
color 0A

echo.
echo ╔═══════════════════════════════════════════════════════════╗
echo ║                                                           ║
echo ║            🔗 DESKTOP SHORTCUTS CREATOR                   ║
echo ║                                                           ║
echo ║            Facebook Marketplace Automation                ║
echo ║                                                           ║
echo ╚═══════════════════════════════════════════════════════════╝
echo.

echo 🔗 Creating desktop shortcuts...
echo.

REM Get current directory
set "CURRENT_DIR=%~dp0"

REM Create VBS script to create shortcuts
echo Set objShell = CreateObject("WScript.Shell") > temp_shortcut.vbs
echo Set objDesktop = objShell.SpecialFolders("Desktop") >> temp_shortcut.vbs
echo. >> temp_shortcut.vbs

REM Main launcher shortcut
echo Set objShortcut = objShell.CreateShortcut(objDesktop ^& "\🚀 Marketplace Automation.lnk") >> temp_shortcut.vbs
echo objShortcut.TargetPath = "%CURRENT_DIR%🚀 Start Marketplace App.bat" >> temp_shortcut.vbs
echo objShortcut.WorkingDirectory = "%CURRENT_DIR%" >> temp_shortcut.vbs
echo objShortcut.Description = "Facebook Marketplace Automation - Full Application" >> temp_shortcut.vbs
echo objShortcut.Save >> temp_shortcut.vbs
echo. >> temp_shortcut.vbs

REM Backend only shortcut
echo Set objShortcut = objShell.CreateShortcut(objDesktop ^& "\🔧 Backend Server.lnk") >> temp_shortcut.vbs
echo objShortcut.TargetPath = "%CURRENT_DIR%🔧 Start Backend Only.bat" >> temp_shortcut.vbs
echo objShortcut.WorkingDirectory = "%CURRENT_DIR%" >> temp_shortcut.vbs
echo objShortcut.Description = "Facebook Marketplace Automation - Backend Server Only" >> temp_shortcut.vbs
echo objShortcut.Save >> temp_shortcut.vbs
echo. >> temp_shortcut.vbs

REM Stop services shortcut
echo Set objShortcut = objShell.CreateShortcut(objDesktop ^& "\🛑 Stop Marketplace Services.lnk") >> temp_shortcut.vbs
echo objShortcut.TargetPath = "%CURRENT_DIR%🛑 Stop All Services.bat" >> temp_shortcut.vbs
echo objShortcut.WorkingDirectory = "%CURRENT_DIR%" >> temp_shortcut.vbs
echo objShortcut.Description = "Stop all Marketplace Automation services" >> temp_shortcut.vbs
echo objShortcut.Save >> temp_shortcut.vbs

REM Execute the VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

echo ✅ Desktop shortcuts created successfully!
echo.
echo 📋 Created shortcuts:
echo    🚀 Marketplace Automation.lnk
echo    🔧 Backend Server.lnk  
echo    🛑 Stop Marketplace Services.lnk
echo.
echo 🎯 You can now launch the application from your desktop!
echo.
pause
