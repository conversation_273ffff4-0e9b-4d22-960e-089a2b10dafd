import 'package:hive/hive.dart';

part 'account.g.dart';

@HiveType(typeId: 0)
class Account extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String email;

  @HiveField(3)
  String? encryptedPassword;

  @HiveField(4)
  AccountStatus status;

  @HiveField(5)
  int? proxyId;

  @HiveField(6)
  DateTime? lastLogin;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  String? sessionData;

  @HiveField(9)
  int postCount;

  @HiveField(10)
  DateTime? lastPostTime;

  @HiveField(11)
  bool isActive;

  Account({
    this.id,
    required this.name,
    required this.email,
    this.encryptedPassword,
    this.status = AccountStatus.inactive,
    this.proxyId,
    this.lastLogin,
    DateTime? createdAt,
    this.sessionData,
    this.postCount = 0,
    this.lastPostTime,
    this.isActive = true,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'status': status.name,
      'proxyId': proxyId,
      'lastLogin': lastLogin?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'postCount': postCount,
      'lastPostTime': lastPostTime?.toIso8601String(),
      'isActive': isActive,
    };
  }

  // Create from JSON response
  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      status: AccountStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => AccountStatus.inactive,
      ),
      proxyId: json['proxyId'],
      lastLogin: json['lastLogin'] != null 
          ? DateTime.parse(json['lastLogin']) 
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      postCount: json['postCount'] ?? 0,
      lastPostTime: json['lastPostTime'] != null 
          ? DateTime.parse(json['lastPostTime']) 
          : null,
      isActive: json['isActive'] ?? true,
    );
  }

  // Copy with method for updates
  Account copyWith({
    int? id,
    String? name,
    String? email,
    String? encryptedPassword,
    AccountStatus? status,
    int? proxyId,
    DateTime? lastLogin,
    DateTime? createdAt,
    String? sessionData,
    int? postCount,
    DateTime? lastPostTime,
    bool? isActive,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      encryptedPassword: encryptedPassword ?? this.encryptedPassword,
      status: status ?? this.status,
      proxyId: proxyId ?? this.proxyId,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      sessionData: sessionData ?? this.sessionData,
      postCount: postCount ?? this.postCount,
      lastPostTime: lastPostTime ?? this.lastPostTime,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Account(id: $id, name: $name, email: $email, status: $status)';
  }
}

@HiveType(typeId: 1)
enum AccountStatus {
  @HiveField(0)
  inactive,

  @HiveField(1)
  active,

  @HiveField(2)
  banned,

  @HiveField(3)
  suspended,

  @HiveField(4)
  testing,

  @HiveField(5)
  error,
}

extension AccountStatusExtension on AccountStatus {
  String get displayName {
    switch (this) {
      case AccountStatus.inactive:
        return 'غير نشط';
      case AccountStatus.active:
        return 'نشط';
      case AccountStatus.banned:
        return 'محظور';
      case AccountStatus.suspended:
        return 'معلق';
      case AccountStatus.testing:
        return 'قيد الاختبار';
      case AccountStatus.error:
        return 'خطأ';
    }
  }

  String get displayNameEn {
    switch (this) {
      case AccountStatus.inactive:
        return 'Inactive';
      case AccountStatus.active:
        return 'Active';
      case AccountStatus.banned:
        return 'Banned';
      case AccountStatus.suspended:
        return 'Suspended';
      case AccountStatus.testing:
        return 'Testing';
      case AccountStatus.error:
        return 'Error';
    }
  }
}
