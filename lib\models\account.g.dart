// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AccountAdapter extends TypeAdapter<Account> {
  @override
  final int typeId = 0;

  @override
  Account read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Account(
      id: fields[0] as int?,
      name: fields[1] as String,
      email: fields[2] as String,
      encryptedPassword: fields[3] as String?,
      status: fields[4] as AccountStatus,
      proxyId: fields[5] as int?,
      lastLogin: fields[6] as DateTime?,
      createdAt: fields[7] as DateTime?,
      sessionData: fields[8] as String?,
      postCount: fields[9] as int,
      lastPostTime: fields[10] as DateTime?,
      isActive: fields[11] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Account obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.encryptedPassword)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.proxyId)
      ..writeByte(6)
      ..write(obj.lastLogin)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.sessionData)
      ..writeByte(9)
      ..write(obj.postCount)
      ..writeByte(10)
      ..write(obj.lastPostTime)
      ..writeByte(11)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AccountStatusAdapter extends TypeAdapter<AccountStatus> {
  @override
  final int typeId = 1;

  @override
  AccountStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AccountStatus.inactive;
      case 1:
        return AccountStatus.active;
      case 2:
        return AccountStatus.banned;
      case 3:
        return AccountStatus.suspended;
      case 4:
        return AccountStatus.testing;
      case 5:
        return AccountStatus.error;
      default:
        return AccountStatus.inactive;
    }
  }

  @override
  void write(BinaryWriter writer, AccountStatus obj) {
    switch (obj) {
      case AccountStatus.inactive:
        writer.writeByte(0);
        break;
      case AccountStatus.active:
        writer.writeByte(1);
        break;
      case AccountStatus.banned:
        writer.writeByte(2);
        break;
      case AccountStatus.suspended:
        writer.writeByte(3);
        break;
      case AccountStatus.testing:
        writer.writeByte(4);
        break;
      case AccountStatus.error:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AccountStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
